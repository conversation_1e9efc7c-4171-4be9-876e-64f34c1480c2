<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Collection;

use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

/**
 * @template GenericType
 *
 * @extends Collection<GenericType>
 */
readonly class PaginatedCollection extends Collection
{
    /**
     * @param array<GenericType> $items
     */
    public function __construct(
        #[Groups(['Default'])]
        #[SerializedName('total_items')]
        public int $totalItems,

        array $items,
    ) {
        parent::__construct(items: $items);
    }
}
