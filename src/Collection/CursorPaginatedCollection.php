<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Collection;

use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

/**
 * @template GenericType
 *
 * @extends Collection<GenericType>
 */
readonly class CursorPaginatedCollection extends Collection
{
    /**
     * @param array<GenericType> $items
     */
    public function __construct(
        array $items,

        #[Groups(['Default'])]
        #[SerializedName('next_page_token')]
        public ?string $nextPageToken = null,
    ) {
        parent::__construct(items: $items);
    }
}
