<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\ErrorResponse;

use Symfony\Component\Serializer\Attribute\Groups;

readonly class ApiErrorResponse
{
    public const string GROUP_DEFAULT = 'default-error';
    public const string GROUP_VALIDATION = 'validation-error';

    /**
     * @param array<ValidationError>|null $violations List of validation errors
     */
    public function __construct(
        #[Groups([self::GROUP_DEFAULT, self::GROUP_VALIDATION])]
        public string $type,

        #[Groups([self::GROUP_DEFAULT, self::GROUP_VALIDATION])]
        public string $title,

        #[Groups([self::GROUP_DEFAULT, self::GROUP_VALIDATION])]
        public ?string $detail = null,

        #[Groups([self::GROUP_DEFAULT])]
        public ?string $translation = null,

        #[Groups([self::GROUP_VALIDATION])]
        public ?array $violations = null,
    ) {
    }
}
