<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\ErrorResponse;

readonly class ValidationError
{
    /**
     * @param string|null $field   The name of/path to the field that caused the error. Null if the error is not related to a specific field.
     * @param string      $message The error message
     */
    public function __construct(
        public ?string $field,
        public string $message,
    ) {
    }
}
