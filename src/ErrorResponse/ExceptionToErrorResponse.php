<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\ErrorResponse;

use PreZero\ApiBundle\ApiRequestMetadataResolver;
use PreZero\ApiBundle\Exception\ApiExceptionInterface;
use PreZero\ApiBundle\Exception\ValidationException;
use PreZero\ApiBundle\JsonResponse;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\HttpExceptionInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;

#[AsEventListener]
readonly class ExceptionToErrorResponse
{
    public function __construct(
        private ApiRequestMetadataResolver $apiResources,
        private LoggerInterface $logger,
    ) {
    }

    public function __invoke(ExceptionEvent $event): void
    {
        $apiMetadata = $this->apiResources->resolveCurrentOperation($event->getRequest());

        if (null === $apiMetadata) {
            return;
        }

        $exception = $event->getThrowable();

        $this->logger->error(
            sprintf('Exception occurred in %s API area request', $apiMetadata->resourceMetadata->area),
            ['exception' => $exception],
        );

        if ($exception instanceof ValidationException) {
            $event->setResponse(
                new JsonResponse(
                    data: new ApiErrorResponse(
                        type: 'validation_error',
                        title: 'Validation error',
                        detail: $exception->getMessage(),
                        violations: $exception->getValidationErrors(),
                    ),
                    status: 422,
                )
            );

            return;
        }

        if ($exception instanceof BadRequestHttpException) {
            $event->setResponse(
                new JsonResponse(
                    data: new ApiErrorResponse(
                        type: 'bad_request_data',
                        title: 'Request cannot be processed.',
                        detail: $exception->getMessage(),
                    ),
                    status: 0 === $exception->getCode() ? 400 : $exception->getCode(),
                )
            );

            return;
        }

        if (
            $exception instanceof AuthenticationException
            || $exception->getPrevious() instanceof AuthenticationException
        ) {
            $event->setResponse(
                new JsonResponse(
                    data: new ApiErrorResponse(
                        type: 'authentication_exception',
                        title: 'Authentication failed',
                        detail: $exception->getMessage(),
                    ),
                    status: 401,
                )
            );

            return;
        }

        if ($exception instanceof HttpExceptionInterface && 403 === $exception->getStatusCode()) {
            $event->setResponse(
                new JsonResponse(
                    data: new ApiErrorResponse(
                        type: 'access_denied',
                        title: 'Access denied',
                        detail: $exception->getMessage(),
                    ),
                    status: 403,
                )
            );

            return;
        }

        if ($exception instanceof HttpExceptionInterface) {
            $event->setResponse(
                new JsonResponse(
                    data: new ApiErrorResponse(
                        type: 'http_exception',
                        title: $exception->getMessage(),
                        detail: $exception->getMessage(),
                    ),
                    status: 0 === $exception->getStatusCode() ? 400 : $exception->getStatusCode(),
                )
            );

            return;
        }

        if ($exception instanceof ApiExceptionInterface) {
            $event->setResponse(
                new JsonResponse(
                    data: new ApiErrorResponse(
                        type: $exception->getType(),
                        title: $exception->getTitle(),
                        detail: $exception->getDetail(),
                        translation: $exception->getTranslation(),
                    ),
                    status: $exception->getCode()
                )
            );
        }
    }
}
