<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\EventListener;

use PreZero\ApiBundle\ApiRequestMetadataResolver;
use Psr\Log\LoggerInterface;
use Psr\Log\LogLevel;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpKernel\Event\ResponseEvent;

#[AsEventListener(priority: -9)]
readonly class LogHttpResponses
{
    public function __construct(
        private ApiRequestMetadataResolver $apiResources,
        private LoggerInterface $logger,
    ) {
    }

    public function __invoke(ResponseEvent $responseEvent): void
    {
        $request = $responseEvent->getRequest();
        $apiMetadata = $this->apiResources->resolveCurrentOperation($request);

        if (null === $apiMetadata) {
            return;
        }

        $response = $responseEvent->getResponse();
        $responseCode = $response->getStatusCode();
        $responseCodesLogLevel = $apiMetadata->operationMetadata->responseCodesLogLevel;

        if (array_key_exists($responseCode, $responseCodesLogLevel)) {
            $logLevel = $responseCodesLogLevel[$responseCode];
        } elseif ($responseCode >= 400 && $responseCode < 500) {
            $logLevel = LogLevel::ERROR;
        } elseif ($responseCode >= 500) {
            $logLevel = LogLevel::CRITICAL;
        } else {
            $logLevel = LogLevel::INFO;
        }

        $this->logger->log(
            $logLevel,
            sprintf('HTTP Response from %s API area', $apiMetadata->resourceMetadata->area),
            [
                'method' => $request->getMethod(),
                'uri' => $request->getUri(),
                'status_code' => $response->getStatusCode(),
                'headers' => $response->headers->all(),
                'content' => $response->getContent(),
            ],
        );
    }
}
