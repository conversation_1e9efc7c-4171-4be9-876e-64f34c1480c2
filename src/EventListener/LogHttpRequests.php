<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle\EventListener;

use PreZero\ApiBundle\ApiRequestMetadataResolver;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpKernel\Event\RequestEvent;

#[AsEventListener(priority: 10)]
readonly class LogHttpRequests
{
    public function __construct(
        private ApiRequestMetadataResolver $apiResources,
        private LoggerInterface $logger,
    ) {
    }

    public function __invoke(RequestEvent $requestEvent): void
    {
        $request = $requestEvent->getRequest();
        $apiMetadata = $this->apiResources->resolveCurrentOperation($request);

        if (null === $apiMetadata) {
            return;
        }

        $this->logger->info(
            sprintf('HTTP Request to %s API area', $apiMetadata->resourceMetadata->area),
            [
                'method' => $request->getMethod(),
                'uri' => $request->getUri(),
                'headers' => $request->headers->all(),
                'content' => $request->getContent(),
            ],
        );
    }
}
