<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle\EventListener;

use PreZ<PERSON>\ApiBundle\ApiRequestMetadataResolver;
use PreZero\ApiBundle\Exception\ApiBundleException;
use PreZero\ApiBundle\JsonResponse;
use PreZero\ApiBundle\Serializer\SerializerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ResponseEvent;

#[AsEventListener(priority: -8)]
readonly class JsonResponseFormatter
{
    public function __construct(
        private ApiRequestMetadataResolver $apiResources,
        private SerializerInterface $serializer,
    ) {
    }

    /**
     * @throws ApiBundleException
     */
    public function __invoke(ResponseEvent $event): void
    {
        $apiMetadata = $this->apiResources->resolveCurrentOperation($event->getRequest());

        if (null === $apiMetadata) {
            return;
        }

        $response = $event->getResponse();

        if (!$response instanceof JsonResponse) {
            return;
        }

        $headers = $response->headers;
        $headers->set('Content-Type', 'application/json');

        try {
            $content = $this->serializer->serialize($response->getDataForSerializing());
        } catch (ApiBundleException $e) {
            throw new ApiBundleException(
                'Failed to serialize json response content',
                previous: $e,
            );
        }

        $event->setResponse(new Response(
            content: $content,
            status: $response->getStatusCode(),
            headers: $headers->allPreserveCase(),
        ));
    }
}
