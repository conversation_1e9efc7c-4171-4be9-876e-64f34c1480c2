<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\EventListener;

use PreZero\ApiBundle\ApiRequestMetadataResolver;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Event\RequestEvent;

#[AsEventListener(priority: 4)]
readonly class RequireRequestParameters
{
    public function __construct(
        private ApiRequestMetadataResolver $apiResources,
    ) {
    }

    public function __invoke(RequestEvent $event): void
    {
        $request = $event->getRequest();
        $apiMetadata = $this->apiResources->resolveCurrentOperation($request);

        if (null === $apiMetadata || !$event->isMainRequest()) {
            return;
        }

        $operation = $apiMetadata->operationMetadata;

        // Require headers
        foreach ($operation->headers as $header) {
            if ($header->required) {
                $this->requireNonEmptyHeader($request, $header->name);
            }
        }

        // Require query parameters
        foreach ($operation->queryParameters as $queryParameter) {
            if ($queryParameter->required) {
                $this->requireNonEmptyQueryParameter($request, $queryParameter->name, $queryParameter->multipleValues);
            }
        }

        // Require filters (same as query params, maybe we can unify them at some point)
        foreach ($operation->filters as $filter) {
            if ($filter->required) {
                $this->requireNonEmptyQueryParameter($request, $filter->parameterName, false);
            }
        }
    }

    private function requireNonEmptyHeader(Request $request, string $headerName): void
    {
        if (!$request->headers->has($headerName)) {
            throw new BadRequestException(sprintf('Header "%s" is required', $headerName));
        }

        if (empty($request->headers->get($headerName))) {
            throw new BadRequestException(sprintf('Header "%s" cannot be empty', $headerName));
        }
    }

    private function requireNonEmptyQueryParameter(Request $request, string $parameterName, bool $multipleValues): void
    {
        if (!$request->query->has($parameterName)) {
            throw new BadRequestException(sprintf('Query parameter "%s" is required', $parameterName));
        }

        if ($multipleValues) {
            $values = $request->query->all($parameterName);

            foreach ($values as $value) {
                if (empty($value)) {
                    throw new BadRequestException(sprintf('Query parameter "%s" cannot have empty value', $parameterName));
                }
            }

            return;
        }

        if (empty($request->query->get($parameterName))) {
            throw new BadRequestException(sprintf('Query parameter "%s" cannot be empty', $parameterName));
        }
    }
}
