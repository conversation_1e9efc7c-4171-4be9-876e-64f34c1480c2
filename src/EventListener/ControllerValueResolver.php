<?php

declare(strict_types=1);

namespace Pre<PERSON><PERSON>\ApiBundle\EventListener;

use PreZ<PERSON>\ApiBundle\ApiRequestMetadataResolver;
use PreZero\ApiBundle\ErrorResponse\ValidationError;
use PreZero\ApiBundle\Exception\ApiBundleException;
use PreZero\ApiBundle\Exception\ValidationException;
use PreZero\ApiBundle\Serializer\SerializerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\Attribute\AutoconfigureTag;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Controller\ValueResolverInterface;
use Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadata;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[AutoconfigureTag(name: 'controller.argument_value_resolver', attributes: ['priority' => 1000])]
readonly class ControllerValueResolver implements ValueResolverInterface
{
    public function __construct(
        private SerializerInterface $serializer,
        private ValidatorInterface $validator,
        private ApiRequestMetadataResolver $apiRequestMetadataResolver,
        private LoggerInterface $logger,
    ) {
    }

    /**
     * @return iterable<object>
     */
    public function resolve(Request $request, ArgumentMetadata $argument): iterable
    {
        $apiRequestMetadata = $this->apiRequestMetadataResolver->resolveCurrentOperation($request);
        $argumentType = $argument->getType();

        if (
            null === $apiRequestMetadata
            || !in_array($request->getMethod(), ['POST', 'PUT', 'PATCH'], true)
            || null === $argumentType
            || !class_exists($argumentType)
        ) {
            return [];
        }

        [$resourceMetadata, $operationMetadata] = [$apiRequestMetadata->resourceMetadata, $apiRequestMetadata->operationMetadata];
        $expectedType = $operationMetadata->input ?? $resourceMetadata->resourceClass;

        if ($argumentType !== $expectedType) {
            return [];
        }

        try {
            $dto = $this->serializer->deserialize(
                data: $request->getContent(),
                type: $expectedType,
                attributes: $operationMetadata->denormalizationContext,
            );
        } catch (ApiBundleException $e) {
            $this->logger->error(
                message: 'Failed to deserialize request body',
                context: [
                    'exception' => $e,
                    'request' => [
                        'method' => $request->getMethod(),
                        'uri' => $request->getRequestUri(),
                        'body' => $request->getContent(),
                    ],
                ],
            );

            throw new BadRequestHttpException(
                message: sprintf(
                    'Invalid request body. Check if JSON data format matches expected data structure. Error: %s',
                    $e->getMessage(),
                ),
                previous: $e,
            );
        }

        $errors = $this->validator->validate($dto);

        if ($errors->count() > 0) {
            $validationErrors = [];

            foreach ($errors as $error) {
                $validationErrors[] = new ValidationError(
                    field: $error->getPropertyPath() ?: null,
                    message: (string) $error->getMessage(),
                );
            }

            $this->logger->warning(
                message: 'Failed to validate request body',
                context: [
                    'errors' => $validationErrors,
                    'request' => [
                        'method' => $request->getMethod(),
                        'uri' => $request->getRequestUri(),
                        'body' => $request->getContent(),
                    ],
                ],
            );

            throw new ValidationException(
                message: 'Failed to validate request contents.',
                validationErrors: $validationErrors,
            );
        }

        return [$dto];
    }
}
