<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle\EventListener;

use PreZero\ApiBundle\ApiRequestMetadataResolver;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;

#[AsEventListener(event: KernelEvents::REQUEST, priority: 5)]
readonly class DenyAccessListener
{
    public function __construct(
        private AuthorizationCheckerInterface $authorizationChecker,
        private ApiRequestMetadataResolver $apiResources,
    ) {
    }

    public function __invoke(RequestEvent $event): void
    {
        $apiMetadata = $this->apiResources->resolveCurrentOperation($event->getRequest());

        if (null === $apiMetadata || !$event->isMainRequest()) {
            return;
        }

        $resource = $apiMetadata->resourceMetadata;
        $operation = $apiMetadata->operationMetadata;

        if (null !== $operation->security) {
            if (!$this->authorizationChecker->isGranted(new Expression($operation->security))) {
                throw new AccessDeniedException();
            }

            return;
        }

        if (
            null !== $resource->security
            && !$this->authorizationChecker->isGranted(new Expression($resource->security))
        ) {
            throw new AccessDeniedException();
        }
    }
}
