<?php

declare(strict_types=1);

namespace PreZero\ApiBundle;

use Symfony\Component\HttpFoundation\Response;

class JsonResponse extends Response
{
    private readonly mixed $data;

    /**
     * @param array<string, string> $headers
     */
    public function __construct(
        mixed $data = null,
        int $status = 200,
        array $headers = [],
    ) {
        $this->data = $data;
        parent::__construct('Internal error: For internal usage only!', $status, $headers);
    }

    public function getDataForSerializing(): mixed
    {
        return $this->data;
    }
}
