<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle\Metadata;

use PreZ<PERSON>\ApiBundle\Enum\FilterType;
use Symfony\Component\Validator\Constraint;

readonly class FilterMetadata
{
    /**
     * @param string       $parameterName        Parameter name in the query string
     * @param FilterType   $filterType           How we should search for this value in the storage (full match, or partial match, or just starting with the value, etc.)
     * @param ?string      $fieldName            Field name in the (DB) entity, which we will query for (Optional)
     * @param bool         $required             Whether the parameter is required in the query string
     * @param string       $parameterType        Type of the parameter in the query string (string, integer, etc.)
     * @param ?string      $parameterDescription Description of the parameter in the query string (Optional)
     * @param ?string      $parameterFormat      Format of the parameter in the query string (Optional)
     * @param ?string[]    $parameterEnumValues  List of allowed values for the query parameter (Optional)
     * @param Constraint[] $validators           Array of Symfony constraints to validate the parameter
     * @param bool         $multiple             Whether the filter accepts multiple values
     */
    public function __construct(
        public string $parameterName,
        public FilterType $filterType = FilterType::STRING_EXACT,
        public ?string $fieldName = null,
        public bool $required = false,
        public string $parameterType = 'string',
        public ?string $parameterDescription = null,
        public ?string $parameterFormat = null,
        public ?array $parameterEnumValues = null,
        public array $validators = [],
        public bool $multiple = false,
    ) {
    }
}
