<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Metadata;

use PreZero\ApiBundle\Attribute\Value\Header;

readonly class HeaderMetadata
{
    /**
     * @param array<string>|null $enumValues
     */
    public function __construct(
        public string $name,
        public ?string $format = null,
        public string $description = '',
        public bool $required = false,
        public ?array $enumValues = null,
        public ?string $example = null,
        public string $type = 'string',
    ) {
    }

    public static function fromAttribute(Header $header): self
    {
        return new self(
            name: $header->name,
            format: $header->format,
            description: $header->description,
            required: $header->required,
            enumValues: $header->enumValues,
            example: $header->example,
            type: $header->type,
        );
    }
}
