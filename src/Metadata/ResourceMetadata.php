<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Metadata;

readonly class ResourceMetadata
{
    /**
     * @param class-string             $resourceClass
     * @param array<OperationMetadata> $operations
     * @param array<int>               $perPageOptions
     */
    public function __construct(
        public string $resourceClassNamespace,
        public string $resourceClass,
        public string $resourceClassShortName,
        public string $name,
        public string $area,
        public ?string $identifier,
        public string $pathPrefix,
        public string $tag,
        public ?string $security,
        public array $operations,
        public array $perPageOptions,
        public int $defaultPerPage,
    ) {
    }

    /**
     * @param array<OperationMetadata> $operations
     */
    public function withOperations(array $operations): self
    {
        return new self(
            $this->resourceClassNamespace,
            $this->resourceClass,
            $this->resourceClassShortName,
            $this->name,
            $this->area,
            $this->identifier,
            $this->pathPrefix,
            $this->tag,
            $this->security,
            $operations,
            $this->perPageOptions,
            $this->defaultPerPage
        );
    }

    public function getOperation(string $operationName): OperationMetadata
    {
        foreach ($this->operations as $operation) {
            if ($operation->name === $operationName) {
                return $operation;
            }
        }

        throw new \RuntimeException(
            sprintf('Operation "%s" not found in resource "%s".', $operationName, $this->resourceClass)
        );
    }
}
