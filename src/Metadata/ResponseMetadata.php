<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle\Metadata;

use Pre<PERSON>ero\ApiBundle\Attribute\Value\Response;
use Pre<PERSON>ero\ApiBundle\Enum\ContentType;

readonly class ResponseMetadata
{
    /**
     * @param class-string|null               $output
     * @param ?array<string, scalar|string[]> $normalizationContext
     */
    public function __construct(
        public int $httpCode,
        public string $description,
        public ?string $mimeType,
        public ?string $output = null,
        public ?ContentType $contentType = null,
        public ?string $openApiSchemaName = null,
        public ?array $normalizationContext = null,
    ) {
    }

    public static function fromAttribute(Response $response): self
    {
        return new self(
            httpCode: $response->httpCode,
            description: $response->description,
            mimeType: $response->mimeType,
            output: $response->output,
            contentType: $response->contentType,
            openApiSchemaName: $response->openApiSchemaName,
            normalizationContext: $response->normalizationContext,
        );
    }
}
