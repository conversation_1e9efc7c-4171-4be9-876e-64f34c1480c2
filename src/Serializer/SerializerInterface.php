<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle\Serializer;

use PreZ<PERSON>\ApiBundle\Exception\ApiBundleException;

interface SerializerInterface
{
    /**
     * Serializes data into a string.
     *
     * @param array<string, scalar|string[]> $attributes
     *
     * @throws ApiBundleException
     */
    public function serialize(mixed $data, array $attributes = []): string;

    /**
     * Deserializes data into the given type.
     *
     * @template TObject of object
     *
     * @param array<string, scalar|string[]> $attributes
     * @param class-string<TObject>          $type
     *
     * @phpstan-return TObject
     *
     * @throws ApiBundleException
     */
    public function deserialize(string $data, string $type, array $attributes = []): object;

    /**
     * Denormalized data into the given type.
     *
     * @template TObject of object
     *
     * @param array<string, scalar|string[]> $attributes
     * @param class-string<TObject>          $type
     *
     * @phpstan-return TObject
     *
     * @throws ApiBundleException
     */
    public function denormalize(mixed $data, string $type, array $attributes = []): object;
}
