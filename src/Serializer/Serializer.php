<?php

declare(strict_types=1);

namespace <PERSON><PERSON>ero\ApiBundle\Serializer;

use PreZ<PERSON>\ApiBundle\Exception\ApiBundleException;
use Vuryss\Serializer\SerializerException;

class Serializer implements SerializerInterface
{
    private \Vuryss\Serializer\Serializer $serializer;

    public function serialize(mixed $data, array $attributes = []): string
    {
        try {
            return $this->getSerializer()->serialize($data, attributes: $attributes);
        } catch (SerializerException $e) {
            throw new ApiBundleException($e->getMessage(), $e->getCode(), $e);
        }
    }

    public function deserialize(string $data, string $type, array $attributes = []): object
    {
        try {
            return $this->getSerializer()->deserialize($data, $type, attributes: $attributes);
        } catch (SerializerException $e) {
            throw new ApiBundleException($e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * Denormalized data into the given type.
     *
     * @template T of object
     *
     * @param class-string<T> $type
     *
     * @phpstan-return T
     *
     * @throws ApiBundleException
     */
    public function denormalize(mixed $data, string $type, array $attributes = []): object
    {
        try {
            /** @var T $object */
            $object = $this->getSerializer()->denormalize($data, $type, attributes: $attributes);

            return $object;
        } catch (SerializerException $e) {
            throw new ApiBundleException($e->getMessage(), $e->getCode(), $e);
        }
    }

    private function getSerializer(): \Vuryss\Serializer\Serializer
    {
        if (!isset($this->serializer)) {
            // TODO: Add caching
            $this->serializer = new \Vuryss\Serializer\Serializer();
        }

        return $this->serializer;
    }
}
