<?php

declare(strict_types=1);

namespace PreZero\ApiBundle;

use Symfony\Component\HttpFoundation\Request;

readonly class ApiRequestMetadataResolver
{
    public function __construct(
        private ApiResources $apiResources,
    ) {
    }

    public function resolveCurrentOperation(Request $request): ?ApiRequestMetadata
    {
        $apiResourceClass = $request->attributes->get('_api_resource_class');
        $resourceOperationName = $request->attributes->get('_api_operation_name');

        if (
            !is_string($apiResourceClass)
            || !is_string($resourceOperationName)
            || !class_exists($apiResourceClass)
        ) {
            return null;
        }

        $resource = $this->apiResources->getResourceMetadata($apiResourceClass);
        $operation = $resource->getOperation($resourceOperationName);

        return new ApiRequestMetadata(resourceMetadata: $resource, operationMetadata: $operation);
    }
}
