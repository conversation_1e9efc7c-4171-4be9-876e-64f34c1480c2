<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Exception;

use PreZero\ApiBundle\ErrorResponse\ValidationError;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class ValidationException extends UnprocessableEntityHttpException
{
    /**
     * @param array<ValidationError> $validationErrors
     * @param array<mixed>           $headers
     */
    public function __construct(
        string $message,
        readonly private array $validationErrors,
        ?\Throwable $previous = null,
        int $code = 0,
        array $headers = [],
    ) {
        parent::__construct($message, $previous, $code, $headers);
    }

    /**
     * @return array<ValidationError>
     */
    public function getValidationErrors(): array
    {
        return $this->validationErrors;
    }
}
