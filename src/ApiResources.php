<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle;

use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Events\MetadataBuilt;
use PreZero\ApiBundle\Exception\ApiBundleException;
use PreZero\ApiBundle\Metadata\MetadataBuilder;
use PreZero\ApiBundle\Metadata\ResourceMetadata;
use Symfony\Component\Config\ConfigCache;
use Symfony\Component\Config\Resource\FileResource;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\DependencyInjection\Attribute\AutowireIterator;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\VarExporter\Exception\ExceptionInterface;
use Symfony\Component\VarExporter\VarExporter;

class ApiResources
{
    private bool $isInitialized = false;

    /**
     * @var array<class-string, ResourceMetadata>
     */
    private array $apiResourcesMetadata = [];

    /**
     * @param iterable<object> $resourceClasses
     */
    public function __construct(
        #[Autowire('%kernel.cache_dir%')]
        private readonly string $cacheDir,

        #[Autowire('%kernel.debug%')]
        private readonly bool $debug,

        #[AutowireIterator(tag: 'api.resource')]
        private readonly iterable $resourceClasses,

        private readonly MetadataBuilder $metadataBuilder,

        private readonly EventDispatcherInterface $eventDispatcher,
    ) {
    }

    /**
     * @return array<ResourceMetadata>
     *
     * @throws ApiBundleException
     */
    public function getResourcesMetadataForApiArea(string $apiArea): array
    {
        $this->initialize();

        return array_filter(
            $this->apiResourcesMetadata,
            static fn (ResourceMetadata $resourceMetadata): bool => $resourceMetadata->area === $apiArea
        );
    }

    /**
     * @param class-string $resourceClass
     *
     * @throws ApiBundleException
     */
    public function getResourceMetadata(string $resourceClass): ResourceMetadata
    {
        $this->initialize();

        if (!isset($this->apiResourcesMetadata[$resourceClass])) {
            throw new ApiBundleException(sprintf(
                'Resource metadata for class "%s" not found. Did you forget to register it as a service with the "api.resource" tag?',
                $resourceClass
            ));
        }

        return $this->apiResourcesMetadata[$resourceClass];
    }

    /**
     * @throws ApiBundleException
     */
    private function initialize(): void
    {
        if ($this->isInitialized) {
            return;
        }

        $cacheFile = $this->cacheDir.'/api_resources_metadata.php';
        $cacheConfig = new ConfigCache($cacheFile, $this->debug);

        if (!$cacheConfig->isFresh()) {
            $this->rebuildCache($cacheConfig);

            return;
        }

        /** @phpstan-ignore-next-line well (shrug) can't explain that to phpstan either */
        $this->apiResourcesMetadata = require $cacheFile;
        $this->isInitialized = true;
    }

    /**
     * @throws ApiBundleException
     */
    private function rebuildCache(ConfigCache $cacheConfig): void
    {
        $resources = [];

        foreach ($this->resourceClasses as $resourceClass) {
            $reflectionClass = ReflectionUtil::reflectionClass($resourceClass);
            $apiResourceAttributes = $reflectionClass->getAttributes(ApiResource::class);

            if ([] === $apiResourceAttributes) {
                continue;
            }

            if (1 < count($apiResourceAttributes)) {
                throw new \LogicException(sprintf('Class "%s" has more than one ApiResource attribute.', $resourceClass::class));
            }

            $fileName = $reflectionClass->getFileName();

            if (false === $fileName) {
                continue;
            }

            $resources[] = new FileResource($fileName);
            $className = $reflectionClass->getName();
            $apiResource = $apiResourceAttributes[0]->newInstance();
            $resourceMetadata = $this->metadataBuilder->buildFromApiResource($className, $apiResource);
            $this->apiResourcesMetadata[$className] = $resourceMetadata;
        }

        $this->eventDispatcher->dispatch(new MetadataBuilt($this->apiResourcesMetadata));

        try {
            $code = '<?php return '.VarExporter::export($this->apiResourcesMetadata).';';
        } catch (ExceptionInterface $e) {
            throw new \RuntimeException('Failed to export API resources metadata.', $e->getCode(), previous: $e);
        }

        $cacheConfig->write($code, $resources);
        $this->isInitialized = true;
    }
}
