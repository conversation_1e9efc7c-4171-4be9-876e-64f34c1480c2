<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\UserCriteria;

readonly class UserFilter
{
    /**
     * @param non-empty-string|array<non-empty-string> $value
     */
    public function __construct(
        public string $field,
        public string|array $value,
        public MatchStrategy $matchStrategy,
    ) {
    }

    /**
     * @return non-empty-string
     */
    public function getSingleValue(): string
    {
        if (is_array($this->value)) {
            return $this->value[0];
        }

        return $this->value;
    }

    /**
     * @return array<non-empty-string>
     */
    public function getValues(): array
    {
        if (is_array($this->value)) {
            return $this->value;
        }

        return [$this->value];
    }
}
