<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\UserCriteria;

enum MatchStrategy: string
{
    case EXACT = 'exact';
    case PARTIAL = 'partial';
    case START = 'start';
    case END = 'end';

    public function toLikePattern(string $value): string
    {
        // @link https://stackoverflow.com/questions/2843009/how-to-escape-like-var-with-doctrine
        $value = addcslashes($value, '%_');

        return match ($this) {
            self::EXACT => $value,
            self::PARTIAL => '%'.$value.'%',
            self::START => $value.'%',
            self::END => '%'.$value,
        };
    }
}
