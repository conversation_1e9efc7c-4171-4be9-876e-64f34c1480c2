<?php

declare(strict_types=1);

namespace PreZero\ApiBundle;

use Symfony\Component\String\Slugger\SluggerInterface;

use function Symfony\Component\String\u;

readonly class PathFormatter
{
    public function __construct(
        private SluggerInterface $slugger,
    ) {
    }

    public function format(string $string): string
    {
        return $this->slugger->slug(u($string)->snake()->lower()->toString())->toString();
    }
}
