<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle;

use PreZero\ApiBundle\Enum\FilterType;
use PreZero\ApiBundle\ErrorResponse\ValidationError;
use PreZero\ApiBundle\Exception\ValidationException;
use PreZero\ApiBundle\Metadata\OperationMetadata;
use PreZero\ApiBundle\UserCriteria\MatchStrategy;
use PreZero\ApiBundle\UserCriteria\UserFilter;
use PreZero\ApiBundle\UserCriteria\UserSearchCriteria;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class UserSearchCriteriaBuilder
{
    private OperationMetadata $operationMetadata;

    public function __construct(
        private readonly ApiRequestMetadataResolver $apiResources,
        private readonly ValidatorInterface $validator,
    ) {
    }

    public function fromSymfonyRequest(Request $request): UserSearchCriteria
    {
        $this->operationMetadata = $this->getCurrentOperationMetadata($request);

        return new UserSearchCriteria(
            page: $this->extractPageFromSymfonyRequest($request),
            perPage: $this->extractPerPageFromSymfonyRequest($request),
            nextPageToken: $this->extractNextPageTokenFromSymfonyRequest($request),
            filters: $this->constructFiltersFromSymfonyRequest($request),
        );
    }

    private function extractPageFromSymfonyRequest(Request $request): int
    {
        $page = (int) $request->query->get('page', '1');

        if ($page < 1) {
            throw new UnprocessableEntityHttpException('Page must be greater than 0');
        }

        return $page;
    }

    private function extractPerPageFromSymfonyRequest(Request $request): int
    {
        $perPage = (int) $request->query->get('per-page', $this->operationMetadata->defaultPerPage);

        if (!in_array($perPage, $this->operationMetadata->perPageOptions, true)) {
            throw new UnprocessableEntityHttpException(sprintf(
                'Per page must be one of the following values: %s',
                implode(', ', $this->operationMetadata->perPageOptions),
            ));
        }

        return $perPage;
    }

    private function extractNextPageTokenFromSymfonyRequest(Request $request): ?string
    {
        $nextPageToken = $request->query->get('next-page-token');

        if ('' === $nextPageToken) {
            throw new UnprocessableEntityHttpException('Next page token must not be empty');
        }

        if (!is_string($nextPageToken)) {
            return null;
        }

        return $nextPageToken;
    }

    /**
     * @return array<string, UserFilter>
     */
    private function constructFiltersFromSymfonyRequest(Request $request): array
    {
        $apiFilters = $this->getCurrentOperationMetadata($request)->filters;
        $userFilters = [];

        foreach ($apiFilters as $apiFilter) {
            // Symfony relies on lazy loading of the groups inside the constraint, which is implemented in their
            // constructor. However, when they are cached and loaded from cache, the property is set to null and not
            // unset. We have to manually unset it to avoid validation errors.
            foreach ($apiFilter->validators as $validator) {
                if (null === $validator->groups) {
                    /** @phpstan-ignore unset.possiblyHookedProperty */
                    unset($validator->groups);
                }
            }

            $processedValue = null;
            $validationErrors = [];

            if ($apiFilter->multiple) {
                // Handle multiple values using PHP's array syntax (e.g., key[]=value1&key[]=value2)
                // $request->query->all() retrieves all query parameters.
                // PHP automatically parses 'key[]' into an array under 'key'.
                $queryParameters = $request->query->all();
                $values = $queryParameters[$apiFilter->parameterName] ?? null;

                if (!is_array($values) || [] === $values) {
                    continue;
                }

                $processedValue = [];

                foreach ($values as $value) {
                    if (!is_string($value) || '' === $value) {
                        continue;
                    }

                    $errors = $this->validator->validate($value, $apiFilter->validators);

                    if (count($errors) > 0) {
                        foreach ($errors as $error) {
                            $validationErrors[] = new ValidationError(
                                field: $apiFilter->parameterName,
                                message: (string) $error->getMessage(),
                            );
                        }
                    } else {
                        $processedValue[] = $value;
                    }
                }
            } else {
                // Handle single value using get()
                $rawValue = $request->query->get($apiFilter->parameterName);

                if (!is_string($rawValue) || '' === $rawValue) {
                    continue;
                }

                $errors = $this->validator->validate($rawValue, $apiFilter->validators);

                if (count($errors) > 0) {
                    foreach ($errors as $error) {
                        $validationErrors[] = new ValidationError(
                            field: $apiFilter->parameterName,
                            message: (string) $error->getMessage(),
                        );
                    }
                } else {
                    $processedValue = $rawValue;
                }
            }

            if (!empty($validationErrors)) {
                throw new ValidationException(
                    message: 'Invalid value(s) for query parameter',
                    validationErrors: $validationErrors,
                );
            }

            if (empty($processedValue)) {
                continue;
            }

            $userFilters[$apiFilter->parameterName] = new UserFilter(
                field: $apiFilter->fieldName ?? $apiFilter->parameterName,
                value: $processedValue,
                matchStrategy: match ($apiFilter->filterType) {
                    FilterType::STRING_EXACT => MatchStrategy::EXACT,
                    FilterType::STRING_PARTIAL => MatchStrategy::PARTIAL,
                    FilterType::STRING_START => MatchStrategy::START,
                    FilterType::STRING_END => MatchStrategy::END,
                },
            );
        }

        return $userFilters;
    }

    private function getCurrentOperationMetadata(Request $request): OperationMetadata
    {
        $apiRequestMetadata = $this->apiResources->resolveCurrentOperation($request);

        if (null === $apiRequestMetadata) {
            throw new \RuntimeException('Cannot resolve current operation metadata. Is this an API Resource endpoint?');
        }

        return $apiRequestMetadata->operationMetadata;
    }
}
