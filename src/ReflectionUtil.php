<?php

declare(strict_types=1);

namespace PreZero\ApiBundle;

use PreZero\ApiBundle\Exception\ApiBundleException;

/**
 * @internal
 */
readonly class ReflectionUtil
{
    /**
     * @template T of object
     *
     * @param class-string<T>|T $classNameOrObject
     *
     * @return \ReflectionClass<T>
     *
     * @throws ApiBundleException
     */
    public static function reflectionClass(string|object $classNameOrObject): \ReflectionClass
    {
        try {
            return new \ReflectionClass($classNameOrObject);
        } catch (\ReflectionException $e) {
            throw new ApiBundleException(
                sprintf(
                    'Class "%s" not found. Make sure the class exists and is able to be autoloaded.',
                    is_string($classNameOrObject) ? $classNameOrObject : get_class($classNameOrObject),
                ),
                previous: $e
            );
        }
    }
}
