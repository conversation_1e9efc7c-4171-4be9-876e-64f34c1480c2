<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Controller;

use PreZero\ApiBundle\Documentation\OpenApiSpecificationProvider;
use PreZero\ApiBundle\Exception\ApiBundleException;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
class OpenApiSpecController extends AbstractController
{
    public function __construct(
        private readonly OpenApiSpecificationProvider $openApiSpecificationProvider,
    ) {
    }

    /**
     * @throws ApiBundleException
     */
    public function __invoke(string $area): Response
    {
        return new JsonResponse(
            data: $this->openApiSpecificationProvider->getSpecificationForArea($area),
        );
    }
}
