<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle\Controller;

use PreZero\ApiBundle\Documentation\OpenApiSpecificationProvider;
use PreZero\ApiBundle\Exception\ApiBundleException;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
class SwaggerUiController extends AbstractController
{
    public function __construct(
        private readonly OpenApiSpecificationProvider $openApiSpecificationProvider,
    ) {
    }

    /**
     * @throws ApiBundleException
     */
    public function __invoke(string $area): Response
    {
        $spec = $this->openApiSpecificationProvider->getSpecificationForArea($area);
        $jsonSpec = json_encode($spec);

        return $this->render(
            '@Api/swagger-ui.html.twig',
            [
                'spec' => $jsonSpec,
                'title' => $spec->info->title.' Documentation',
            ]
        );
    }
}
