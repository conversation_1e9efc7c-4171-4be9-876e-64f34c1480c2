<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle\Command;

use Pre<PERSON>ero\ApiBundle\Documentation\OpenApiSpecificationProvider;
use PreZero\ApiBundle\Serializer\SerializerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'prezero:api:dump-documentation', description: 'Dump the API documentation')]
class DumpDocumentationCommand extends Command
{
    public function __construct(
        private readonly OpenApiSpecificationProvider $openApiSpecificationProvider,
        private readonly SerializerInterface $serializer,
    ) {
        parent::__construct();
    }

    #[\Override]
    protected function configure(): void
    {
        $this
            ->addArgument(
                name: 'area',
                mode: InputArgument::REQUIRED,
                description: 'Which API area to dump?',
            )
            ->addArgument(
                name: 'fileName',
                mode: InputArgument::OPTIONAL,
                description: 'File name to dump the documentation to, if not specified, it will be dumped to stdout',
            )
            ->addOption(
                name: 'format',
                mode: InputOption::VALUE_NONE,
                description: 'Pretty print the JSON output',
            );
    }

    /**
     * @throws \PreZero\ApiBundle\Exception\ApiBundleException
     */
    #[\Override]
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $area = $input->getArgument('area');
        assert(is_string($area));

        $fileName = $input->getArgument('fileName');
        assert(is_string($fileName) || null === $fileName);

        $openApiSpecification = $this->openApiSpecificationProvider->getSpecificationForArea($area);

        $json = $this->serializer->serialize($openApiSpecification);

        if ($input->getOption('format')) {
            $json = json_encode(json_decode($json), JSON_PRETTY_PRINT);
            assert(is_string($json));
        }

        if (null === $fileName) {
            $output->writeln($json);
        } else {
            file_put_contents($fileName, $json);
            $output->writeln(sprintf('Documentation dumped to "%s"', $fileName));
        }

        return Command::SUCCESS;
    }
}
