<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle\Attribute\Operation;

use Pre<PERSON><PERSON>\ApiBundle\Attribute\Value\Filter;
use Pre<PERSON>ero\ApiBundle\Attribute\Value\Header;
use Pre<PERSON>ero\ApiBundle\Attribute\Value\PathParameter;
use Pre<PERSON>ero\ApiBundle\Attribute\Value\QueryParameter;
use PreZero\ApiBundle\Attribute\Value\Response;
use PreZero\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Enum\Pagination;

abstract class Operation
{
    public const string HTTP_METHOD = 'GET';

    /**
     * @param array<PathParameter>|null $pathParameters
     * @param array<QueryParameter>     $queryParameters
     * @param array<Response>           $responses
     * @param array<string, mixed>      $normalizationContext
     * @param array<string, mixed>      $denormalizationContext
     * @param array<Filter>             $filters
     * @param ?class-string             $input
     * @param ?class-string             $output
     * @param array<Header>             $additionalRequestHeaders
     * @param array<int, string>        $responseCodesLogLevel
     * @param array<int>|null           $perPageOptions
     */
    public function __construct(
        public string $controller,
        public string $controllerAction,
        public ?string $name,
        public string $summary,
        public string $description,
        public ?string $security,

        // Request
        public ?string $uriTemplate,
        public int $routePriority,
        public ?array $pathParameters,
        public array $queryParameters,
        public bool $omitDefaultQueryParameters,
        public ContentType $requestType,
        public string $requestDescription,
        public array $denormalizationContext,
        public array $filters,
        public ?string $input,
        public array $additionalRequestHeaders,
        public ?string $requestOpenApiSchemaName,

        // Response
        public Pagination $pagination,
        public ?array $perPageOptions,
        public ?int $defaultPerPage,
        public string $responseDescription,
        public array $responses,
        public ContentType $responseType,
        public array $normalizationContext,
        public ?string $output,
        public int $successHttpCode,
        public ?string $responseOpenApiSchemaName,
        public array $responseCodesLogLevel,
    ) {
    }
}
