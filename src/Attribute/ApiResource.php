<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Attribute;

use PreZero\ApiBundle\Attribute\Operation\Operation;

#[\Attribute(\Attribute::TARGET_CLASS)]
class ApiResource
{
    /**
     * @param array<Operation> $operations
     * @param array<int>|null  $perPageOptions
     */
    public function __construct(
        public string $area,
        public array $operations,
        public ?string $name = null,
        public ?string $identifier = 'id',
        public string $tag = 'Unsorted',
        public ?string $security = null,
        public ?array $perPageOptions = null,
        public ?int $defaultPerPage = null,
    ) {
    }
}
