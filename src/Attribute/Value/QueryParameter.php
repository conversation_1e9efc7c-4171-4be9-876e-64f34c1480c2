<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Attribute\Value;

readonly class QueryParameter
{
    /**
     * @param array<int, scalar>|null $enumValues
     */
    public function __construct(
        public string $name,
        public string $type,
        public ?string $format = null,
        public string $description = '',
        public bool $required = false,
        public ?array $enumValues = null,
        public bool $multipleValues = false,
    ) {
    }
}
