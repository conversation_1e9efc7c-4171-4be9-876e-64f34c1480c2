<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle\Attribute\Value;

use PreZero\ApiBundle\Enum\ContentType;

readonly class Response
{
    /**
     * @param class-string|null               $output               If null, the output will be the Resource that defines the operation
     * @param ContentType|null                $contentType          The content type of the response. Defaults to operation's content type.
     * @param ?array<string, scalar|string[]> $normalizationContext Normalization context for this specific response. `null` inherits from operation, `[]` overrides with empty.
     */
    public function __construct(
        public int $httpCode,
        public string $description,
        public ?string $mimeType = null,
        public ?string $output = null,
        public ?ContentType $contentType = null,
        public ?string $openApiSchemaName = null,
        public ?array $normalizationContext = null,
    ) {
    }
}
