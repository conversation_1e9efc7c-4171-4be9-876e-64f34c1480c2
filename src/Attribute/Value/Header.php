<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Attribute\Value;

readonly class Header
{
    /**
     * @param array<string>|null $enumValues
     */
    public function __construct(
        public string $name,
        public ?string $format = null,
        public string $description = '',
        public bool $required = false,
        public ?array $enumValues = null,
        public ?string $example = null,
        public string $type = 'string',
    ) {
    }
}
