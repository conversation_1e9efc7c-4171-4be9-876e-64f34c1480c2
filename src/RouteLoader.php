<?php

declare(strict_types=1);

namespace PreZero\ApiBundle;

use Symfony\Bundle\FrameworkBundle\Routing\RouteLoaderInterface;
use Symfony\Component\Config\Resource\DirectoryResource;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Routing\Route;
use Symfony\Component\Routing\RouteCollection;

readonly class RouteLoader implements RouteLoaderInterface
{
    public function __construct(
        private Configuration $configuration,
        private ApiResources $apiResources,
        private Filesystem $filesystem,
        #[Autowire('%kernel.project_dir%')]
        private string $projectDir,
    ) {
    }

    public function __invoke(): RouteCollection
    {
        $routeCollection = new RouteCollection();

        foreach ($this->configuration->getBundleConfiguration()['areas'] as $areaName => $areaConfiguration) {
            $resourcePath = $this->filesystem->readlink($this->projectDir.'/'.$areaConfiguration['resource_path'], true);

            if (null === $resourcePath) {
                throw new \LogicException(sprintf(
                    'Api area "%s" resource path "%s" does not exist. Check configuration under "api.areas"',
                    $areaName,
                    $areaConfiguration['resource_path'],
                ));
            }

            $routeCollection->addResource(new DirectoryResource($resourcePath));

            foreach ($this->apiResources->getResourcesMetadataForApiArea($areaName) as $resourceMetadata) {
                foreach ($resourceMetadata->operations as $operation) {
                    $route = new Route(
                        path: $operation->fullUrlTemplate,
                        defaults: [
                            '_controller' => $operation->controller.'::'.$operation->controllerAction,
                            '_format' => 'json',
                            '_stateless' => $areaConfiguration['stateless'] ?? true,
                            '_api_area' => $areaName,
                            '_api_resource_class' => $resourceMetadata->resourceClass,
                            '_api_operation_name' => $operation->name,
                        ],
                        requirements: $operation->urlRequirements,
                        methods: [$operation->method],
                    );

                    $routeCollection->add($operation->name, $route, priority: $operation->routePriority);
                }
            }
        }

        return $routeCollection;
    }
}
