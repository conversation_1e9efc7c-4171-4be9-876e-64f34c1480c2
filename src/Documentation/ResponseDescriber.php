<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Documentation;

use <PERSON>k<PERSON>\OpenApi\MediaType;
use Dok<PERSON>\OpenApi\Operation;
use Dok<PERSON>\OpenApi\Response;
use <PERSON>k<PERSON>\OpenApi\Schema;
use <PERSON>k<PERSON>\Undefined;
use PreZero\ApiBundle\Attribute\Operation\Delete;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetArray;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Operation\Put;
use PreZero\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Enum\Pagination;
use PreZero\ApiBundle\ErrorResponse\ApiErrorResponse;
use PreZero\ApiBundle\Metadata\OperationMetadata;
use PreZero\ApiBundle\Metadata\ResourceMetadata;
use PreZero\ApiBundle\Metadata\ResponseMetadata;

readonly class ResponseDescriber
{
    public function __construct(
        private Dokky $dokky,
    ) {
    }

    public function describeResponse(
        ResourceMetadata $resourceMetadata,
        OperationMetadata $operationMetadata,
        Operation $openApiOperation,
    ): void {
        $openApiOperation->responses = Undefined::VALUE === $openApiOperation->responses
            ? []
            : $openApiOperation->responses;

        $this->describeSuccessResponse($resourceMetadata, $operationMetadata, $openApiOperation);

        // Add exception responses to all operations
        /** @phpstan-ignore-next-line https://github.com/phpstan/phpstan/issues/7880 */
        $openApiOperation->responses['400'] = new Response(
            description: 'Bad request',
            content: [
                'application/json' => new MediaType(
                    schema: new Schema(
                        ref: $this->dokky->componentsRegistry()->getSchemaReference(
                            className: ApiErrorResponse::class,
                            groups: [ApiErrorResponse::GROUP_DEFAULT],
                        )
                    )
                ),
            ]
        );

        /** @phpstan-ignore-next-line https://github.com/phpstan/phpstan/issues/7880 */
        $openApiOperation->responses['401'] = new Response(
            description: 'Not authenticated',
            content: [
                'application/json' => new MediaType(
                    schema: new Schema(
                        ref: $this->dokky->componentsRegistry()->getSchemaReference(
                            className: ApiErrorResponse::class,
                            groups: [ApiErrorResponse::GROUP_DEFAULT],
                        )
                    )
                ),
            ]
        );

        /** @phpstan-ignore-next-line https://github.com/phpstan/phpstan/issues/7880 */
        $openApiOperation->responses['403'] = new Response(
            description: 'Access to resource denied',
            content: [
                'application/json' => new MediaType(
                    schema: new Schema(
                        ref: $this->dokky->componentsRegistry()->getSchemaReference(
                            className: ApiErrorResponse::class,
                            groups: [ApiErrorResponse::GROUP_DEFAULT],
                        )
                    )
                ),
            ]
        );

        /** @phpstan-ignore-next-line https://github.com/phpstan/phpstan/issues/7880 */
        $openApiOperation->responses['404'] = new Response(
            description: 'Resource not found',
            content: [
                'application/json' => new MediaType(
                    schema: new Schema(
                        ref: $this->dokky->componentsRegistry()->getSchemaReference(
                            className: ApiErrorResponse::class,
                            groups: [ApiErrorResponse::GROUP_DEFAULT],
                        )
                    )
                ),
            ]
        );

        /** @phpstan-ignore-next-line https://github.com/phpstan/phpstan/issues/7880 */
        $openApiOperation->responses['422'] = new Response(
            description: 'Request validation failed',
            content: [
                'application/json' => new MediaType(
                    schema: new Schema(
                        ref: $this->dokky->componentsRegistry()->getNamedSchemaReference(
                            className: ApiErrorResponse::class,
                            schemaName: 'ApiValidationError',
                            groups: [ApiErrorResponse::GROUP_VALIDATION],
                        )
                    )
                ),
            ]
        );

        $this->describeManuallyDefinedResponses($resourceMetadata, $operationMetadata, $openApiOperation);
    }

    private function describeSuccessResponse(
        ResourceMetadata $resourceMetadata,
        OperationMetadata $operationMetadata,
        Operation $openApiOperation,
    ): void {
        if (ContentType::EMPTY === $operationMetadata->responseType) {
            /** @phpstan-ignore-next-line https://github.com/phpstan/phpstan/issues/7880 */
            $openApiOperation->responses[$operationMetadata->successHttpCode] = new Response(
                description: $operationMetadata->responseDescription ?: 'Success, no response content',
            );

            return;
        }

        if (ContentType::BINARY === $operationMetadata->responseType) {
            /** @phpstan-ignore-next-line https://github.com/phpstan/phpstan/issues/7880 */
            $openApiOperation->responses[$operationMetadata->successHttpCode] = new Response(
                description: $operationMetadata->responseDescription ?: 'Binary content',
                content: [
                    'application/octet-stream' => new MediaType(
                        schema: new Schema(
                            type: Schema\Type::STRING,
                            format: 'binary',
                        ),
                    ),
                ],
            );
        } else {
            // Describing default DTO responses
            match ($operationMetadata->type) {
                Get::class => $this->describeGetResponses($operationMetadata, $resourceMetadata, $openApiOperation),
                GetCollection::class => $this->describeGetCollectionResponses(
                    $operationMetadata,
                    $resourceMetadata,
                    $openApiOperation
                ),
                GetArray::class => $this->describeGetArrayResponses(
                    $operationMetadata,
                    $resourceMetadata,
                    $openApiOperation
                ),
                Post::class => $this->describePostResponses($operationMetadata, $resourceMetadata, $openApiOperation),
                Put::class => $this->describePutResponses($operationMetadata, $resourceMetadata, $openApiOperation),
                Patch::class => $this->describePatchResponses($operationMetadata, $resourceMetadata, $openApiOperation),
                Delete::class => $this->describeDeleteResponses($operationMetadata, $resourceMetadata, $openApiOperation),
                default => throw new \RuntimeException('Unsupported operation type: '.$operationMetadata->type),
            };
        }
    }

    private function describeGetResponses(
        OperationMetadata $operationMetadata,
        ResourceMetadata $resourceMetadata,
        Operation $openApiOperation,
    ): void {
        /** @var string[]|null $groups */
        $groups = $operationMetadata->normalizationContext['groups'] ?? null;
        $schemaRef = $this->getSchemaRef($operationMetadata, $resourceMetadata, $groups);

        /** @phpstan-ignore-next-line https://github.com/phpstan/phpstan/issues/7880 */
        $openApiOperation->responses[$operationMetadata->successHttpCode] = new Response(
            description: $operationMetadata->responseDescription ?: 'Data for single '.$resourceMetadata->resourceClassShortName,
            content: [
                'application/json' => new MediaType(
                    schema: new Schema(ref: $schemaRef),
                ),
            ]
        );
    }

    private function describeGetCollectionResponses(
        OperationMetadata $operationMetadata,
        ResourceMetadata $resourceMetadata,
        Operation $openApiOperation,
    ): void {
        /** @var string[]|null $groups */
        $groups = $operationMetadata->normalizationContext['groups'] ?? null;
        $schemaRef = $this->getSchemaRef($operationMetadata, $resourceMetadata, $groups);

        $itemsSchema = new Schema(
            type: Schema\Type::ARRAY,
            items: new Schema(ref: $schemaRef),
        );

        $topLevelProperties = match ($operationMetadata->pagination) {
            Pagination::NONE => [
                'items' => $itemsSchema,
            ],
            Pagination::PAGE_BASED => [
                'total_items' => new Schema(type: Schema\Type::INTEGER),
                'items' => $itemsSchema,
            ],
            Pagination::CURSOR => [
                'next_page_token' => new Schema(
                    anyOf: [
                        new Schema(type: Schema\Type::STRING),
                        new Schema(type: Schema\Type::NULL),
                    ]
                ),
                'items' => $itemsSchema,
            ],
        };

        /** @phpstan-ignore-next-line https://github.com/phpstan/phpstan/issues/7880 */
        $openApiOperation->responses[$operationMetadata->successHttpCode] = new Response(
            description: $operationMetadata->responseDescription ?: 'Collection of '.$resourceMetadata->resourceClassShortName.' resources',
            content: [
                'application/json' => new MediaType(
                    schema: new Schema(
                        type: Schema\Type::OBJECT,
                        properties: $topLevelProperties,
                    ),
                ),
            ]
        );
    }

    private function describeGetArrayResponses(
        OperationMetadata $operationMetadata,
        ResourceMetadata $resourceMetadata,
        Operation $openApiOperation,
    ): void {
        /** @var string[]|null $groups */
        $groups = $operationMetadata->normalizationContext['groups'] ?? null;
        $schemaRef = $this->getSchemaRef($operationMetadata, $resourceMetadata, $groups);

        /** @phpstan-ignore-next-line https://github.com/phpstan/phpstan/issues/7880 */
        $openApiOperation->responses[$operationMetadata->successHttpCode] = new Response(
            description: $operationMetadata->responseDescription ?: 'Array of '.$resourceMetadata->resourceClassShortName.' resources',
            content: [
                'application/json' => new MediaType(
                    schema: new Schema(
                        type: Schema\Type::ARRAY,
                        items: new Schema(ref: $schemaRef),
                    ),
                ),
            ]
        );
    }

    private function describePostResponses(
        OperationMetadata $operationMetadata,
        ResourceMetadata $resourceMetadata,
        Operation $openApiOperation,
    ): void {
        /** @var string[]|null $groups */
        $groups = $operationMetadata->normalizationContext['groups'] ?? null;
        $schemaRef = $this->getSchemaRef($operationMetadata, $resourceMetadata, $groups);

        /** @phpstan-ignore-next-line https://github.com/phpstan/phpstan/issues/7880 */
        $openApiOperation->responses[$operationMetadata->successHttpCode] = new Response(
            description: $operationMetadata->responseDescription ?: 'Resource created',
            content: [
                'application/json' => new MediaType(
                    schema: new Schema(ref: $schemaRef),
                ),
            ]
        );
    }

    private function describePutResponses(
        OperationMetadata $operationMetadata,
        ResourceMetadata $resourceMetadata,
        Operation $openApiOperation,
    ): void {
        /** @var string[]|null $groups */
        $groups = $operationMetadata->normalizationContext['groups'] ?? null;
        $schemaRef = $this->getSchemaRef($operationMetadata, $resourceMetadata, $groups);

        /** @phpstan-ignore-next-line https://github.com/phpstan/phpstan/issues/7880 */
        $openApiOperation->responses[$operationMetadata->successHttpCode] = new Response(
            description: $operationMetadata->responseDescription ?: 'Resource updated',
            content: [
                'application/json' => new MediaType(
                    schema: new Schema(ref: $schemaRef),
                ),
            ]
        );
    }

    private function describePatchResponses(
        OperationMetadata $operationMetadata,
        ResourceMetadata $resourceMetadata,
        Operation $openApiOperation,
    ): void {
        /** @var string[]|null $groups */
        $groups = $operationMetadata->normalizationContext['groups'] ?? null;
        $schemaRef = $this->getSchemaRef($operationMetadata, $resourceMetadata, $groups);

        /** @phpstan-ignore-next-line https://github.com/phpstan/phpstan/issues/7880 */
        $openApiOperation->responses[$operationMetadata->successHttpCode] = new Response(
            description: $operationMetadata->responseDescription ?: 'Resource updated',
            content: [
                'application/json' => new MediaType(
                    schema: new Schema(ref: $schemaRef),
                ),
            ]
        );
    }

    private function describeDeleteResponses(
        OperationMetadata $operationMetadata,
        ResourceMetadata $resourceMetadata,
        Operation $openApiOperation,
    ): void {
        /** @var string[]|null $groups */
        $groups = $operationMetadata->normalizationContext['groups'] ?? null;
        $schemaRef = $this->getSchemaRef($operationMetadata, $resourceMetadata, $groups);

        /** @phpstan-ignore-next-line https://github.com/phpstan/phpstan/issues/7880 */
        $openApiOperation->responses[$operationMetadata->successHttpCode] = new Response(
            description: $operationMetadata->responseDescription ?: 'Resource deleted',
            content: [
                'application/json' => new MediaType(
                    schema: new Schema(ref: $schemaRef),
                ),
            ]
        );
    }

    private function describeManuallyDefinedResponses(
        ResourceMetadata $resourceMetadata,
        OperationMetadata $operationMetadata,
        Operation $openApiOperation,
    ): void {
        foreach ($operationMetadata->responses as $overwriteOpenApiResponse) {
            /** @phpstan-ignore-next-line https://github.com/phpstan/phpstan/issues/7880 */
            $openApiOperation->responses[$overwriteOpenApiResponse->httpCode] = $this->constructOpenApiResponse(
                $operationMetadata,
                $resourceMetadata,
                $overwriteOpenApiResponse,
            );
        }
    }

    private function constructOpenApiResponse(
        OperationMetadata $operationMetadata,
        ResourceMetadata $resourceMetadata,
        ResponseMetadata $response,
    ): Response {
        $context = null !== $response->normalizationContext
            ? $response->normalizationContext
            : $operationMetadata->normalizationContext;

        /** @var string[]|null $groups */
        $groups = $context['groups'] ?? null;

        if (null !== $response->output) {
            $schemaRef = null === $response->openApiSchemaName
                ? $this->dokky->componentsRegistry()->getSchemaReference(
                    className: $response->output,
                    groups: $groups,
                )
                : $this->dokky->componentsRegistry()->getNamedSchemaReference(
                    className: $response->output,
                    schemaName: $response->openApiSchemaName,
                    groups: $groups,
                );

            return new Response(
                description: $response->description,
                content: [
                    'application/json' => new MediaType(
                        schema: new Schema(ref: $schemaRef),
                    ),
                ],
            );
        }

        $responseType = $response->contentType ?? $operationMetadata->responseType;

        return match ($responseType) {
            ContentType::DTO => new Response(
                description: $response->description,
                content: [
                    'application/json' => new MediaType(
                        schema: new Schema(ref: $this->getSchemaRef($operationMetadata, $resourceMetadata, $groups)),
                    ),
                ],
            ),
            ContentType::BINARY => new Response(
                description: $response->description,
                content: [
                    $response->mimeType ?? 'application/octet-stream' => new MediaType(
                        schema: new Schema(
                            type: Schema\Type::STRING,
                            format: 'binary',
                        ),
                    ),
                ],
            ),
            ContentType::EMPTY => new Response(
                description: $response->description,
            ),
        };
    }

    /**
     * @param array<string>|null $groups
     */
    private function getSchemaRef(
        OperationMetadata $operationMetadata,
        ResourceMetadata $resourceMetadata,
        ?array $groups,
    ): string {
        if (null === $operationMetadata->responseOpenApiSchemaName) {
            return $this->dokky->componentsRegistry()->getSchemaReference(
                className: $operationMetadata->output ?? $resourceMetadata->resourceClass,
                groups: $groups,
            );
        }

        return $this->dokky->componentsRegistry()->getNamedSchemaReference(
            className: $operationMetadata->output ?? $resourceMetadata->resourceClass,
            schemaName: $operationMetadata->responseOpenApiSchemaName,
            groups: $groups,
        );
    }
}
