<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Documentation;

use <PERSON><PERSON><PERSON>\ClassSchemaGenerator\ClassSchemaGenerator;
use <PERSON><PERSON><PERSON>\ClassSchemaGeneratorInterface;
use <PERSON><PERSON><PERSON>\ComponentsGenerator;
use <PERSON><PERSON><PERSON>\ComponentsRegistry;
use <PERSON>k<PERSON>\Configuration as DokkyConfiguration;
use PreZero\ApiBundle\Configuration;

class Dokky
{
    private ComponentsGenerator $componentsGenerator;
    private ComponentsRegistry $componentsRegistry;
    private ClassSchemaGeneratorInterface $classSchemaGenerator;

    public function __construct(
        private readonly Configuration $configuration,
    ) {
    }

    public function componentsGenerator(): ComponentsGenerator
    {
        if (!isset($this->componentsGenerator)) {
            $this->componentsGenerator = new ComponentsGenerator(
                componentsRegistry: $this->componentsRegistry(),
                classSchemaGenerator: $this->classSchemaGenerator(),
            );
        }

        return $this->componentsGenerator;
    }

    public function componentsRegistry(): ComponentsRegistry
    {
        if (!isset($this->componentsRegistry)) {
            $this->componentsRegistry = new ComponentsRegistry();
        }

        return $this->componentsRegistry;
    }

    public function classSchemaGenerator(): ClassSchemaGeneratorInterface
    {
        if (!isset($this->classSchemaGenerator)) {
            $configuration = new DokkyConfiguration(
                considerNullablePropertiesAsNotRequired: $this->configuration->getBundleConfiguration()['consider_nullable_properties_as_optional'] ?? false,
            );

            $this->classSchemaGenerator = new ClassSchemaGenerator(
                componentsRegistry: $this->componentsRegistry(),
                configuration: $configuration,
            );
        }

        return $this->classSchemaGenerator;
    }
}
