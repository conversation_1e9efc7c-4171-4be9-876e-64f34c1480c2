<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle\Documentation;

use Dok<PERSON>\OpenApi\OpenApi;
use PreZero\ApiBundle\ApiResources;
use PreZero\ApiBundle\Exception\ApiBundleException;
use PreZero\ApiBundle\ReflectionUtil;
use Symfony\Component\Config\ConfigCache;
use Symfony\Component\Config\Resource\FileResource;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\VarExporter\Exception\ExceptionInterface;
use Symfony\Component\VarExporter\VarExporter;

class OpenApiSpecificationProvider
{
    /**
     * @var array<string, bool>
     */
    private array $isAreaInitialized = [];

    /**
     * @var array<string, OpenApi>
     */
    private array $openApiAreaSpecifications = [];

    public function __construct(
        #[Autowire('%kernel.cache_dir%')]
        private readonly string $cacheDir,

        #[Autowire('%kernel.debug%')]
        private readonly bool $debug,

        private readonly ApiResources $apiResources,

        private readonly OpenApiSpecificationGenerator $generator,

        private readonly Dokky $dokky,
    ) {
    }

    /**
     * @throws ApiBundleException
     */
    public function getSpecificationForArea(string $area): OpenApi
    {
        if (!($this->isAreaInitialized[$area] ?? false)) {
            $this->initializeArea($area);
        }

        if (!isset($this->openApiAreaSpecifications[$area])) {
            throw new ApiBundleException(sprintf('No OpenAPI specification found for area "%s".', $area));
        }

        return $this->openApiAreaSpecifications[$area];
    }

    /**
     * @throws ApiBundleException
     */
    private function initializeArea(string $area): void
    {
        $cacheFile = $this->cacheDir.sprintf('/openapi_specification_%s.php', $area);
        $cacheConfig = new ConfigCache($cacheFile, $this->debug);

        if (!$cacheConfig->isFresh()) {
            $this->rebuildCache($cacheConfig, $area);

            return;
        }

        /** @phpstan-ignore-next-line */
        $this->openApiAreaSpecifications[$area] = require $cacheFile;
        $this->isAreaInitialized[$area] = true;
    }

    /**
     * We need to base the cache on the following items:
     * - API Resource classes, because they contain metadata for each operation, it's parameters, requests and responses
     * - All the referenced DTOs, because changes in the DTOs can change the OpenAPI specification
     * - All referenced sub-classes inside each of the DTOs - they can change the OpenAPI specification too
     *
     * @throws ApiBundleException
     */
    private function rebuildCache(ConfigCache $cacheConfig, string $area): void
    {
        $resourcesMetadata = $this->apiResources->getResourcesMetadataForApiArea($area);
        $this->openApiAreaSpecifications[$area] = $this->generator->getSpecificationForArea($area);
        $classNames = [];

        foreach ($resourcesMetadata as $resourceMetadata) {
            $classNames[$resourceMetadata->resourceClass] = true;

            foreach ($resourceMetadata->operations as $operationMetadata) {
                if (null !== $operationMetadata->input) {
                    $classNames[$operationMetadata->input] = true;
                }

                if (null !== $operationMetadata->output) {
                    $classNames[$operationMetadata->output] = true;
                }

                foreach ($operationMetadata->responses as $responseMetadata) {
                    if (null !== $responseMetadata->output) {
                        $classNames[$responseMetadata->output] = true;
                    }
                }
            }
        }

        $classNames = array_unique([
            ...array_keys($classNames),
            ...$this->dokky->componentsRegistry()->getUniqueClassNames(),
        ]);
        $resources = array_map($this->classNameToFileResource(...), $classNames);

        try {
            $code = '<?php return '.VarExporter::export($this->openApiAreaSpecifications[$area]).';';
        } catch (ExceptionInterface $e) {
            throw new ApiBundleException('Failed to cache OpenAPI specifications.', $e->getCode(), previous: $e);
        }

        $cacheConfig->write($code, $resources);
        $this->isAreaInitialized[$area] = true;
    }

    /**
     * @param class-string $className
     *
     * @throws ApiBundleException
     */
    private function classNameToFileResource(string $className): FileResource
    {
        $fileName = ReflectionUtil::reflectionClass($className)->getFileName();

        if (false === $fileName) {
            throw new ApiBundleException(
                sprintf(
                    'Class "%s" has no file name. Make sure the class exists and is able to be autoloaded.',
                    $className,
                )
            );
        }

        return new FileResource($fileName);
    }
}
