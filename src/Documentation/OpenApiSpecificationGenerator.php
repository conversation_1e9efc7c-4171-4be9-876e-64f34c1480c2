<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle\Documentation;

use <PERSON>k<PERSON>\OpenApi\Components;
use Dok<PERSON>\OpenApi\OpenApi;
use Dok<PERSON>\OpenApi\Operation;
use Dok<PERSON>\OpenApi\PathItem;
use Dok<PERSON>\Undefined;
use PreZero\ApiBundle\ApiResources;
use PreZero\ApiBundle\Attribute\Value\Header;
use PreZero\ApiBundle\Configuration;
use PreZero\ApiBundle\Exception\ApiBundleException;
use PreZero\ApiBundle\Metadata\OperationMetadata;
use PreZero\ApiBundle\Metadata\ResourceMetadata;
use PreZero\ApiBundle\Serializer\SerializerInterface;

class OpenApiSpecificationGenerator
{
    /**
     * @var array<Header>
     */
    private array $globalRequestHeaders = [];

    public function __construct(
        private readonly Configuration $configuration,
        private readonly ApiResources $apiResources,
        private readonly SerializerInterface $serializer,
        private readonly RequestDescriber $requestDescriber,
        private readonly ResponseDescriber $responseDescriber,
        private readonly Dokky $dokky,
    ) {
    }

    /**
     * @throws ApiBundleException
     */
    public function getSpecificationForArea(string $area): OpenApi
    {
        $openApi = $this->getInitialOpenApiSpecification($area);
        $resourcesMetadata = $this->apiResources->getResourcesMetadataForApiArea($area);

        if ([] === $resourcesMetadata) {
            return $openApi;
        }

        $this->processGlobalConfiguration($area);

        if (Undefined::VALUE === $openApi->paths) {
            $openApi->paths = [];
        }

        foreach ($resourcesMetadata as $resourceMetadata) {
            foreach ($resourceMetadata->operations as $operation) {
                $this->describeApiResourceOperation($openApi, $operation, $resourceMetadata);
            }
        }

        // Merging components defined from the configuration with generated ones
        $generatedComponents = $this->dokky->componentsGenerator()->generateComponents();

        if (Undefined::VALUE === $openApi->components) {
            $openApi->components = new Components();
        }

        foreach (get_object_vars($openApi->components) as $key => $component) {
            if (Undefined::VALUE === $openApi->components->{$key}) {
                $openApi->components->{$key} = $generatedComponents->{$key};

                continue;
            }

            if (Undefined::VALUE === $generatedComponents->{$key}) {
                continue;
            }

            /** @phpstan-ignore-next-line Can't explain this to php stan :D */
            $openApi->components->{$key} += $generatedComponents->{$key};
        }

        return $openApi;
    }

    /**
     * @throws ApiBundleException
     */
    private function getInitialOpenApiSpecification(string $area): OpenApi
    {
        $areaConfiguration = $this->configuration->getAreaConfiguration($area);
        $areaOpenApi = $areaConfiguration['open_api'] ?? [];
        $areaOpenApi['openapi'] = '3.1.0';

        try {
            return $this->serializer->denormalize($areaOpenApi, OpenApi::class);
        } catch (ApiBundleException $e) {
            throw new ApiBundleException(
                'Invalid OpenAPI specification for area '.$area,
                previous: $e,
            );
        }
    }

    /**
     * @throws ApiBundleException
     */
    private function processGlobalConfiguration(string $area): void
    {
        $areaConfiguration = $this->configuration->getAreaConfiguration($area);

        if (array_key_exists('global_request_headers', $areaConfiguration)) {
            foreach ($areaConfiguration['global_request_headers'] as $name => $headerConfiguration) {
                $this->globalRequestHeaders[] = new Header(
                    $name,
                    $headerConfiguration['format'] ?? null,
                    $headerConfiguration['description'] ?? '',
                    $headerConfiguration['required'] ?? false,
                    $headerConfiguration['enum_values'] ?? null,
                    $headerConfiguration['example'] ?? null,
                    $headerConfiguration['type'] ?? 'string',
                );
            }
        }
    }

    /**
     * @throws ApiBundleException
     */
    private function describeApiResourceOperation(
        OpenApi $api,
        OperationMetadata $operationMetadata,
        ResourceMetadata $resourceMetadata,
    ): void {
        /** @phpstan-ignore-next-line https://github.com/phpstan/phpstan/issues/7880 */
        $api->paths[$operationMetadata->fullUrlTemplate] ??= new PathItem();
        $pathItem = $api->paths[$operationMetadata->fullUrlTemplate];
        $operation = new Operation(
            tags: [$resourceMetadata->tag],
            summary: $operationMetadata->summary,
            description: $operationMetadata->description,
            operationId: $operationMetadata->name,
        );

        match ($operationMetadata->method) {
            'GET' => $pathItem->get = $operation,
            'POST' => $pathItem->post = $operation,
            'PUT' => $pathItem->put = $operation,
            'DELETE' => $pathItem->delete = $operation,
            'PATCH' => $pathItem->patch = $operation,
            default => throw new ApiBundleException('Unsupported HTTP method: '.$operationMetadata->method),
        };

        // Describing request
        $this->requestDescriber
            ->setGlobalRequestHeaders($this->globalRequestHeaders)
            ->describeRequest($resourceMetadata, $operationMetadata, $operation);

        // Describe response
        $this->responseDescriber->describeResponse(
            resourceMetadata: $resourceMetadata,
            operationMetadata: $operationMetadata,
            openApiOperation: $operation
        );
    }
}
