<?php

declare(strict_types=1);

namespace PreZero\ApiBundle;

use PreZero\ApiBundle\Attribute\ApiResource;
use Symfony\Component\Config\Definition\Configurator\DefinitionConfigurator;
use Symfony\Component\DependencyInjection\ChildDefinition;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;
use Symfony\Component\HttpKernel\Bundle\AbstractBundle;

class ApiBundle extends AbstractBundle
{
    /**
     * @param array<mixed> $config
     */
    #[\Override]
    public function loadExtension(array $config, ContainerConfigurator $container, ContainerBuilder $builder): void
    {
        $container->import('../config/services.php');

        $container
            ->services()
            ->get(Configuration::class)
            ->arg('$bundleConfiguration', $config);

        $builder->registerAttributeForAutoconfiguration(
            ApiResource::class,
            static function (ChildDefinition $definition): void {
                $definition
                    ->addTag('api.resource')
                    ->setAutowired(false);
            }
        );
    }

    #[\Override]
    public function prependExtension(ContainerConfigurator $container, ContainerBuilder $builder): void
    {
        $builder->prependExtensionConfig(
            'framework',
            [
                'assets' => [
                    'packages' => [
                        'api-bundle' => [
                            'base_path' => '/bundles/api/',
                        ],
                    ],
                ],
            ],
        );
    }

    #[\Override]
    public function configure(DefinitionConfigurator $definition): void
    {
        /** @see https://symfony.com/doc/current/components/config/definition.html */
        $definition->rootNode()
            ->children()
                ->booleanNode('consider_nullable_properties_as_optional')
                    ->defaultFalse()
                    ->info('Whether to consider nullable properties as optional in the OpenAPI specification')
                    ->end()
                ->arrayNode('per_page_options')
                    ->info('List of allowed values for the "per-page" query parameter')
                    ->scalarPrototype()->end()
                    ->defaultValue(Configuration::DEFAULT_PER_PAGE_OPTIONS)
                    ->end()
                ->scalarNode('default_per_page')
                    ->info('Default value for the "per-page" query parameter')
                    ->defaultValue(Configuration::DEFAULT_PER_PAGE)
                    ->end()
                ->arrayNode('areas')
                    ->useAttributeAsKey('name')
                    ->normalizeKeys(false)
                    ->arrayPrototype()
                        ->children()
                            ->scalarNode('resource_path')
                                ->isRequired()
                                ->cannotBeEmpty()
                                ->info('Path to the directory containing resources - relative to the project root')
                                ->end()
                            ->scalarNode('url_prefix')
                                ->cannotBeEmpty()
                                ->info('URL prefix for the area, defaults to the area name suffixed with -api')
                                ->end()
                            ->booleanNode('stateless')
                                ->defaultTrue()
                                ->info('Whether the area is stateless (no sessions)')
                                ->end()
                            ->arrayNode('per_page_options')
                                ->info('List of allowed values for the "per-page" query parameter')
                                ->scalarPrototype()->end()
                            ->end()
                            ->scalarNode('default_per_page')
                                ->info('Default value for the "per-page" query parameter')
                            ->end()
                            ->arrayNode('global_request_headers')
                                ->useAttributeAsKey('name')
                                ->normalizeKeys(false)
                                ->arrayPrototype()
                                    ->children()
                                        ->enumNode('type')
                                            ->cannotBeEmpty()
                                            ->values(['string', 'number', 'integer', 'boolean'])
                                            ->defaultValue('string')
                                            ->info('Type of the header value, e.g., "string", "number", "integer", etc.')
                                            ->end()
                                        ->scalarNode('format')
                                            ->cannotBeEmpty()
                                            ->info('Additional format for the header value, e.g., "date-time", "uuid", etc.')
                                            ->end()
                                        ->scalarNode('description')
                                            ->cannotBeEmpty()
                                            ->info('Description of the header')
                                            ->end()
                                        ->booleanNode('required')
                                            ->defaultFalse()
                                            ->info('Whether the header is required')
                                            ->end()
                                        ->arrayNode('enum_values')
                                            ->info('Allowed values for the header')
                                            ->scalarPrototype()->end()
                                            ->end()
                                        ->scalarNode('example')
                                            ->cannotBeEmpty()
                                            ->info('Example value for the header')
                                            ->end()
                                    ->end()
                                ->end()
                            ->end()
                            ->arrayNode('open_api')
                                ->useAttributeAsKey('key')
                                ->normalizeKeys(false)
                                ->variablePrototype()->end()
                            ->end()
                        ->end()
                    ->end()
                ->end()
            ->end()
        ;
    }
}
