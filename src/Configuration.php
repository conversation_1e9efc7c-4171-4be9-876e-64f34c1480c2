<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle;

use PreZ<PERSON>\ApiBundle\Exception\ApiBundleException;

/**
 * @phpstan-type Header array{
 *     format: ?string,
 *     description?: string,
 *     required?: bool,
 *     enum_values?: array<string>,
 *     example?: string,
 *     type?: string,
 * }
 * @phpstan-type AreaConfiguration array{
 *     resource_path: string,
 *     url_prefix?: string,
 *     global_request_headers?: array<string, Header>,
 *     open_api?: array<mixed>,
 *     stateless?: bool,
 *     per_page_options: array<int>,
 *     default_per_page?: int,
 * }
 * @phpstan-type BundleConfiguration array{
 *     consider_nullable_properties_as_optional?: bool,
 *     areas: array<string, AreaConfiguration>,
 *     per_page_options: array<int>,
 *     default_per_page: int,
 * }
 *
 * @internal
 */
readonly class Configuration
{
    /**
     * Default values for pagination.
     *
     * @phpstan-var array<int>
     */
    public const array DEFAULT_PER_PAGE_OPTIONS = [5, 10, 25, 50, 100];

    public const int DEFAULT_PER_PAGE = 25;

    /**
     * @param BundleConfiguration $bundleConfiguration
     */
    public function __construct(
        private array $bundleConfiguration,
    ) {
    }

    /**
     * @return BundleConfiguration
     */
    public function getBundleConfiguration(): array
    {
        return $this->bundleConfiguration;
    }

    /**
     * @return AreaConfiguration
     *
     * @throws ApiBundleException
     */
    public function getAreaConfiguration(string $area): array
    {
        if (!isset($this->bundleConfiguration['areas'][$area])) {
            throw new ApiBundleException(
                sprintf(
                    'Area "%s" not found in the bundle configuration. Available areas: %s',
                    $area,
                    implode(', ', array_keys($this->bundleConfiguration['areas'])),
                )
            );
        }

        return $this->bundleConfiguration['areas'][$area];
    }
}
