<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle;

use Pre<PERSON>ero\ApiBundle\Attribute\Operation\GetArray;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Collection\Collection;
use PreZero\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Metadata\OperationMetadata;
use PreZero\ApiBundle\Metadata\ResourceMetadata;
use PreZero\ApiBundle\Serializer\SerializerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ViewEvent;
use Symfony\Component\HttpKernel\KernelEvents;

#[AsEventListener(KernelEvents::VIEW)]
readonly class ResponseFormatter
{
    public function __construct(
        private SerializerInterface $serializer,
        private ApiRequestMetadataResolver $apiResources,
    ) {
    }

    /**
     * @throws Exception\ApiBundleException
     */
    public function __invoke(ViewEvent $event): void
    {
        $apiRequestMetadata = $this->apiResources->resolveCurrentOperation($event->getRequest());

        if (null === $apiRequestMetadata) {
            return;
        }

        $resourceMetadata = $apiRequestMetadata->resourceMetadata;
        $operationMetadata = $apiRequestMetadata->operationMetadata;
        $controllerResult = $event->getControllerResult();

        match ($operationMetadata->responseType) {
            ContentType::EMPTY => $this->returnEmptyResponse($event, $operationMetadata, $controllerResult),
            ContentType::BINARY => $this->returnRawResponse($controllerResult),
            ContentType::DTO => $this->returnSerializedDTO($event, $resourceMetadata, $operationMetadata, $controllerResult),
        };
    }

    private function returnEmptyResponse(
        ViewEvent $event,
        OperationMetadata $operationMetadata,
        mixed $controllerResult,
    ): void {
        if (null !== $controllerResult) {
            throw new \RuntimeException('Expected empty response from the endpoint.');
        }

        $event->setResponse(response: new Response(content: '', status: $operationMetadata->successHttpCode));
    }

    private function returnRawResponse(mixed $controllerResult): void
    {
        if (!$controllerResult instanceof Response) {
            throw new \RuntimeException('Expected Symfony Response from the binary endpoint.');
        }
    }

    /**
     * @throws Exception\ApiBundleException
     */
    private function returnSerializedDTO(
        ViewEvent $event,
        ResourceMetadata $resourceMetadata,
        OperationMetadata $operationMetadata,
        mixed $controllerResult,
    ): void {
        if (GetArray::class === $operationMetadata->type) {
            if (!is_array($controllerResult)) {
                throw new \RuntimeException('Invalid controller output, expected array got '.gettype($controllerResult));
            }
        } else {
            $expectedClass = GetCollection::class === $operationMetadata->type
                ? Collection::class
                : ($operationMetadata->output ?: $resourceMetadata->resourceClass);

            if (!is_object($controllerResult)) {
                throw new \RuntimeException('Invalid controller output, expected object got '.gettype($controllerResult));
            }

            if (!$controllerResult instanceof $expectedClass) {
                throw new \RuntimeException('Invalid controller output, expected '.$expectedClass.' got '.$controllerResult::class);
            }
        }

        $event->setResponse(
            response: new Response(
                content: $this->serializer->serialize(
                    data: $controllerResult,
                    attributes: $operationMetadata->normalizationContext,
                ),
                status: $operationMetadata->successHttpCode,
                headers: ['Content-Type' => 'application/json'],
            ),
        );
    }
}
