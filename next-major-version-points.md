# Possible changes for next major version

- Operation uri requirements can be moved to the path parameter definition.
- Controller and action reference in operation to support formats that symfony router supports and IDE can directly link to controller: [BookingEndpoint::class, 'getCollection'],
- Add validation constraints (pattern, enum, etc) from path parameters to openapi schema
- Change per-page to be maximum limit instead of enum
- Allow plural resource names generation based on global/per-area config
- Support serializer v2
- When generating documentation, take care of same-named classes in different namespaces. If we use the ->shortName() from reflection we would have conflicts.
  - This would happen if the classes are in different namespaces but have the same name and area.
- Allow list of directories for resource classes for area
