# PreZero API Bundle

## Installation

You need to add the bundle to the Symfony project:

```bash
composer require prezero/api-bundle
```

Then, register the bundle in your `config/bundles.php` file:

```php
return [
    // ...
    PreZero\ApiBundle\ApiBundle::class => ['all' => true],
];
```

And also you need to load the routes in your `config/routes.yaml` file:

```yaml
api_bundle:
    resource: '@ApiBundle/config/routes.php'
```

## Configuration

Config file location: `config/packages/api.yaml`

Notable configuration options:
- `consider_nullable_properties_as_optional`: If set to `true`, nullable properties will be considered as optional in the generated OpenAPI schema. This is useful for serializers that skip null values.

### Example config file

```yaml
api:
    consider_nullable_properties_as_optional: true
    areas:
        portal:
            resource_path: 'src/Infrastructure/PortalApi/Resource'
            url_prefix: '/portal-api/v1'
            open_api:
                info:
                    title: Portal API
                    description: Autogenerated documentation for Portal API
                    version: 1.0.0
                components:
                    securitySchemes:
                        basicAuth:
                            type: http
                            scheme: basic
                security:
                    -   basicAuth: [ ]
        hermes_app:
            resource_path: 'src/Infrastructure/MyApi/Resource'
            url_prefix: '/api/v2'
            global_request_headers:
                x-timestamp:
                    format: 'date-time'
                    example: '2024-11-29T13:32:26.154+01:00'
                    description: 'Current timestamp at the moment of sending the request'
                    required: true
            open_api:
                info:
                    title: My API
                    description: Autogenerated documentation for My API
                    version: 2.0.0
                components:
                    securitySchemes:
                        Bearer:
                            type: http
                            scheme: bearer
                            bearerFormat: JWT
                security:
                    -   Bearer: [ ]
```

## Defining API Resources and Operations

This bundle uses PHP attributes to define API resources and their operations (endpoints).

### `#[ApiResource]`

Apply the `#[ApiResource]` attribute to your resource class (e.g., a DTO or resource) to expose it via the API.

```php
<?php

namespace App\Api\Resource;

use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use App\Controller\MyResourceController; // Your controller

#[ApiResource(
    area: 'my_api', // Matches an area defined in config/packages/api.yaml
    name: 'MyResource', // Optional: Defaults to class name. Used in path generation.
    tag: 'My Resources', // Optional: OpenAPI tag for grouping operations.
    identifier: 'id', // Optional: Property name used as the resource identifier (e.g., for GET /resources/{id}). Defaults to 'id'.
    operations: [
        // Define operations here
        new Get(
            controller: MyResourceController::class,
            controllerAction: 'getItem'
        ),
        new GetCollection(
            controller: MyResourceController::class,
            controllerAction: 'getCollection'
            // ... other operation options
        ),
        // ... other operations (Post, Put, Patch, Delete)
    ]
)]
class MyResource
{
    public string $id;
    public string $name;
    public string $status;
    // ... other properties
}
```

### Operation Attributes

 Operations like `Get`, `GetCollection`, `GetArray`, `Post`, `Put`, `Patch`, and `Delete` define the specific endpoints for a resource. They share common configuration options:

 *   `controller`: The controller class handling the request.
*   `controllerAction`: The method within the controller to execute.
*   `name`: Optional unique name for the operation (used for route generation).
*   `uriTemplate`: Optional custom URI template (e.g., `/resources/{id}/activate`). Defaults are generated based on operation type and resource name.
*   `summary`, `description`: OpenAPI documentation strings.
*   `security`: Security requirements (overrides resource/area level).
*   `routePriority`: Optional integer value to control route priority (default: 0). Higher values have higher priority. See [Symfony's route priority documentation](https://symfony.com/doc/current/routing.html#priority-parameter) for more details.
*   `input`, `output`: Specify input/output DTO classes if different from the resource class.
*   `normalizationContext`, `denormalizationContext`: Symfony Serializer context groups.
*   `filters`: An array of `Filter` attributes (see below).
*   `pagination`: Pagination strategy (e.g., `Pagination::PAGE_BASED`, `Pagination::CURSOR`). Applicable primarily to `GetCollection`.
*   `responses`: Array of `Response` attributes defining possible responses.
*   `responseCodesLogLevel`: An array mapping HTTP status codes to PSR-3 log levels (e.g., `[404 => LogLevel::WARNING, 500 => LogLevel::ALERT]`). This controls the log level used by the `LogHttpResponses` listener for specific response codes. If a code is not specified, defaults are used: `LogLevel::INFO` for 2xx/3xx, `LogLevel::ERROR` for 4xx, and `LogLevel::CRITICAL` for 5xx.

### `GetArray` Operation

The `GetArray` operation is similar to `GetCollection` but is designed to return a simple JSON array of resources directly, without the `items` wrapper or pagination metadata. This is useful for endpoints where the client expects a direct array response.

**Example:**

```php
// ... inside #[ApiResource] operations array ...
new GetArray(
   controller: MyResourceController::class,
   controllerAction: 'getArray'
),
// ...
```

A request to this operation would return a response like this:

```json
[
   {"id": "1", "name": "Resource 1"},
   {"id": "2", "name": "Resource 2"}
]
```
*   ... and more specific options per operation type.

### Route Priority

The `routePriority` parameter allows you to control the order in which Symfony evaluates routes. This is particularly useful when you have routes with overlapping patterns where you need to ensure a specific route is matched before others.

**Key Points:**
- Higher priority values are evaluated first
- Default priority is `0` when not specified
- Negative values are supported for lower priority routes
- This maps directly to [Symfony's route priority system](https://symfony.com/doc/current/routing.html#priority-parameter)

**Example Usage:**

```php
#[ApiResource(
    area: 'my_api',
    operations: [
        // High priority route - will be checked first
        new Get(
            controller: MyResourceController::class,
            controllerAction: 'getSpecialItem',
            uriTemplate: '/items/special',
            routePriority: 10
        ),
        // Lower priority route - will be checked after higher priority routes
        new Get(
            controller: MyResourceController::class,
            controllerAction: 'getItem',
            uriTemplate: '/items/{id}',
            routePriority: 0  // or omit for default
        ),
        // Even lower priority route
        new Get(
            controller: MyResourceController::class,
            controllerAction: 'getFallbackItem',
            uriTemplate: '/items/{slug}',
            routePriority: -5
        ),
    ]
)]
class MyResource
{
    // ... resource properties
}
```

In this example:
1. `/items/special` (priority 10) will be matched first
2. `/items/{id}` (priority 0) will be checked second
3. `/items/{slug}` (priority -5) will be checked last

This ensures that the specific `/items/special` route is matched before the more generic `/items/{id}` pattern.

### Filters

Filters allow clients to refine results when fetching collections. Define them using the `Filter` attribute within the `filters` array of an operation.

```php
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Enum\FilterType;
use Symfony\Component\Validator\Constraints as Assert; // For validation

// ... inside #[ApiResource] operations array ...
new GetCollection(
    controller: MyResourceController::class,
    controllerAction: 'getCollection',
    filters: [
        // Simple exact match filter for 'name' property
        new Filter(
            parameterName: 'name', // Query parameter name (?name=...)
            filterType: FilterType::STRING_EXACT // Match strategy
            // fieldName defaults to parameterName if not specified
        ),

        // Partial match filter for 'description', mapping to 'desc' query param
        new Filter(
            parameterName: 'desc',
            fieldName: 'description', // Maps ?desc=... to the 'description' property
            filterType: FilterType::STRING_PARTIAL
        ),

        // Filter with allowed values (enum) and validation
        new Filter(
            parameterName: 'status',
            filterType: FilterType::STRING_EXACT,
            parameterEnumValues: ['active', 'inactive', 'archived'], // OpenAPI enum values
            validators: [ // Server-side validation
                new Assert\Choice(['active', 'inactive', 'archived'])
            ]
        ),

        // Multi-value filter (accepts ?tags[]=tag1&tags[]=tag2)
        new Filter(
            parameterName: 'tags',
            filterType: FilterType::STRING_EXACT, // Strategy applies to each value
            multiple: true // Enable multi-value input
        ),

        // Multi-value filter with validation for each value
        new Filter(
            parameterName: 'ids',
            filterType: FilterType::STRING_EXACT,
            multiple: true,
            validators: [
                new Assert\Uuid() // Each ID in ?ids[]=... must be a valid UUID
            ]
        ),
    ]
),
// ...
```

**Filter Parameters:**

*   `parameterName`: (Required) The name of the query parameter (e.g., `status` for `?status=active`).
*   `filterType`: (Optional) How the filter value is matched (e.g., `FilterType::STRING_EXACT`, `FilterType::STRING_PARTIAL`, `FilterType::STRING_START`, `FilterType::STRING_END`). Defaults to `STRING_EXACT`.
*   `fieldName`: (Optional) The name of the property on the resource/entity to filter by. Defaults to `parameterName`.
*   `multiple`: (Optional) Set to `true` to allow multiple values via array syntax (e.g., `?param[]=value1&param[]=value2`). Defaults to `false`.
*   `required`: (Optional) Whether the filter parameter is required. Defaults to `false`.
*   `parameterType`, `parameterDescription`, `parameterFormat`, `parameterEnumValues`: Used for generating OpenAPI documentation.
*   `validators`: An array of Symfony Validator constraints to apply to the filter value(s).

## Generate OpenAPI json documentation

To generate OpenAPI json documentation and output it directly, run the following command:

```bash
php bin/console prezero:api:dump-documentation
```

To dump it into a file instead - add a second argument with a file name:

```bash
php bin/console prezero:api:dump-documentation my_api.json
```

## Exception handling

This bundle tries to handle exception similarly to RFC 9457 (https://datatracker.ietf.org/doc/html/rfc9457)

If you want the bundle to capture and handle your exception, please implement `\PreZero\ApiBundle\Exception\ApiExceptionInterface`

Example:

```php
use PreZero\ApiBundle\Exception\ApiExceptionInterface;
use Symfony\Component\HttpFoundation\Response;

class AuthException extends \RuntimeException implements ApiExceptionInterface
{
    public const string SESSION_INVALID = 'session_invalid';

    public static function sessionInvalid(): self
    {
        return new self(
            self::SESSION_INVALID,
            'Session invalid.',
            'Device session is not started or user is in different device session.',
            Response::HTTP_UNAUTHORIZED,
        );
    }

    public function __construct(
        private readonly string $type,
        private readonly string $title,
        private readonly ?string $detail,
        int $code = Response::HTTP_UNAUTHORIZED,
    ) {
        parent::__construct(message: $title, code: $code);
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function getDetail(): ?string
    {
        return $this->detail;
    }

    public function getTranslation(): ?string
    {
        return null;
    }
}
```

Otherwise, if you want to handle your exception yourself, but still make use of the ApiBundle serialization for output,
here is an example (note the custom JsonResponse, which is used to make the response pass through the serializer):

```php
use App\Exception\ValidationException;
use PreZero\ApiBundle\JsonResponse;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\Validator\ConstraintViolationInterface;

#[AsEventListener]
readonly class CustomExceptionFormatter
{
    public function __invoke(ExceptionEvent $event): void
    {
        $exception = $event->getThrowable();

        if ($exception instanceof ValidationException) {
            $event->setResponse(
                new JsonResponse(
                    data: [
                        'type' => 'validation_error',
                        'title' => 'Validation error',
                        'detail' => implode(
                            ';',
                            array_map(
                                static fn (ConstraintViolationInterface $violation): string => (string) $violation->getMessage(),
                                $exception->getConstraintViolation()
                            )
                        ),
                    ],
                    status: 0 === $exception->getCode() ? 400 : $exception->getCode(),
                )
            );
        }
    }
}
```

## Using custom configured serializer

By default, the library uses https://github.com/vuryss/serializer with default settings.
Most complex projects will need to configure the serializer to their needs, including some custom denormalizers,
date formats, etc. To do this - implement the `\PreZero\ApiBundle\Serializer\SerializerInterface` like this:

```php
use PreZero\ApiBundle\Exception\ApiBundleException;
use PreZero\ApiBundle\Serializer\SerializerInterface;

readonly class ApiBundleSerializer implements SerializerInterface
{
    public function __construct(
        private \App\Infrastructure\Framework\Serializer\SerializerInterface $serializer,
    ) {
    }

    public function serialize(mixed $data, array $attributes = []): string
    {
        try {
            return $this->serializer->serialize($data, 'json', context: $attributes);
        } catch (SerializerException $e) {
            throw new ApiBundleException($e->getMessage(), $e->getCode(), $e);
        }
    }

    public function deserialize(string $data, string $type, array $attributes = []): object
    {
        try {
            return $this->serializer->deserialize($data, $type, 'json', context: $attributes);
        } catch (SerializerException $e) {
            throw new ApiBundleException($e->getMessage(), $e->getCode(), $e);
        }
    }

    public function denormalize(mixed $data, string $type, array $attributes = []): object
    {
        try {
            return $this->serializer->denormalize($data, $type, context: $attributes);
        } catch (SerializerException $e) {
            throw new ApiBundleException($e->getMessage(), $e->getCode(), $e);
        }
    }
}
```

And then, register the serializer in the service container:

```yaml
services:
    PreZero\ApiBundle\Serializer\SerializerInterface:
        class: App\...\ApiBundleSerializer
```

## Web Accessible documentation

### Swagger UI

Swagger UI is available at `/doc/{api_area}` where `{api_area}` is the name of the area you defined in the configuration.

### Raw OpenAPI json

The raw OpenAPI json is available at `/doc/{api_area}.json` where `{api_area}` is the name of the area you defined in the configuration.

## Contributing

This bundle desperately needs tests. If you want to contribute - please add some tests of the existing features ;)

## Miscellaneous

### Stateful routes

By default, routes are created with a `stateless` flag set to `true`. If you need to have routes with session for some
reason you can overwrite this in the configuration like this:

```yaml
api:
    areas:
        portal:
            # other configuration
            stateless: false
```

### Array schema handling

Array type can usually be declared in several ways, here are the two supported cases:

1. List of elements of given type. Represented as JSON array type. Must be sequentially indexed array.

- `array<Type>`
- `array<int, Type>`
- `Type[]`
- `list<Type>`
- `iterable<Type>`

Example JSON structure:
```json
[
    {/*Type object*/},
    {/*Type object*/}
]
```

2. Associative array of given type. Represented as JSON object type. JSON objects always have string keys, even if
   they are integers in PHP.

- `array<string, Type>`

Example JSON structure:
```json
{
    "key1": {/*Type object*/},
    "key2": {/*Type object*/}
}
```

Even with non-sequential integer keys, still JSON object:
```json
{
    "100": {/*Type object*/},
    "200": {/*Type object*/}
}
```
