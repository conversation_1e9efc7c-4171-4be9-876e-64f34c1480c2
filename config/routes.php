<?php

use Symfony\Component\Routing\Loader\Configurator\RoutingConfigurator;

return static function (RoutingConfigurator $routes): void {
    $routes
        ->add(name: 'api-specification', path: '/doc/{area}.json')
        ->requirements([
            'area' => '[a-zA-Z0-9_-]+',
        ])
        ->methods(['GET'])
        ->controller(\PreZero\ApiBundle\Controller\OpenApiSpecController::class)
    ;

    $routes
        ->add(name: 'api-documentation', path: '/doc/{area}')
        ->requirements([
            'area' => '[a-zA-Z0-9_-]+',
        ])
        ->methods(['GET'])
        ->controller(PreZero\ApiBundle\Controller\SwaggerUiController::class)
    ;

    $routes->import(\PreZero\ApiBundle\RouteLoader::class, 'service');
};
