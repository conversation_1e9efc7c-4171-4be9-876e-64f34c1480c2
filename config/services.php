<?php

use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

use function Symfony\Component\DependencyInjection\Loader\Configurator\service;

return static function (ContainerConfigurator $containerConfigurator): void {
    $services = $containerConfigurator->services()
        ->defaults()
            ->autowire()
            ->autoconfigure()
    ;

    $services
        ->load('PreZero\\ApiBundle\\', __DIR__ . '/../src/')
        ->exclude([
            __DIR__ . '/../src/{DependencyInjection,Entity,Tests,Kernel.php}',
        ]);

    $services->set(\Dokky\ComponentsRegistry::class);
    $services
        ->set(\Dokky\ClassSchemaGenerator\ClassSchemaGenerator::class)
        ->arg('$componentsRegistry', service(\Dokky\ComponentsRegistry::class))
    ;
    $services
        ->set(\Dokky\ComponentsGenerator::class)
        ->arg('$componentsRegistry', service(\Dokky\ComponentsRegistry::class))
        ->arg('$classSchemaGenerator', service(\Dokky\ClassSchemaGenerator\ClassSchemaGenerator::class))
    ;
};
