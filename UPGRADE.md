# Upgrade Guide

This document provides step-by-step instructions for upgrading your application to use the latest version of the API Bundle.

## Version 2.0.0

### Breaking Changes

#### URL Requirements Consolidation

**What Changed:**
- The separate `urlRequirements` property has been removed from all Operation classes
- URL constraints are now defined directly on `PathParameter` objects via a new `constraint` property
- This consolidates URL requirements with path parameter definitions for better organization and consistency

**Migration Required:**
All operations that previously used `urlRequirements` must be updated to use `PathParameter` constraints instead.

#### Before (Old Way)

```php
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Value\PathParameter;

new Get(
    controller: MyController::class,
    controllerAction: 'getItem',
    urlRequirements: ['id' => '\d+', 'slug' => '[a-z0-9-]+'],
    pathParameters: [
        new PathParameter('id', 'integer', 'The item ID'),
        new PathParameter('slug', 'string', 'The item slug'),
    ]
)
```

#### After (New Way)

```php
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Value\PathParameter;

new Get(
    controller: MyController::class,
    controllerAction: 'getItem',
    pathParameters: [
        new PathParameter('id', 'integer', 'The item ID', '\d+'),
        new PathParameter('slug', 'string', 'The item slug', '[a-z0-9-]+'),
    ]
)
```

### Migration Steps

1. **Identify Operations with URL Requirements**
   
   Search your codebase for operations that use the `urlRequirements` parameter:
   ```bash
   grep -r "urlRequirements" src/
   ```

2. **Update Each Operation**
   
   For each operation found:
   
   a. **Remove the `urlRequirements` parameter**
   
   b. **Add or update `pathParameters`** to include constraints:
      - If `pathParameters` already exist, add the `constraint` parameter to the corresponding `PathParameter`
      - If `pathParameters` don't exist, create them with the constraints from `urlRequirements`
   
   c. **Match parameter names** between `urlRequirements` keys and `PathParameter` names

3. **Example Migration**

   **Before:**
   ```php
   new Put(
       controller: ProductController::class,
       controllerAction: 'updateProduct',
       urlRequirements: [
           'categoryId' => '\d+',
           'productId' => '[a-f0-9-]{36}',
       ],
       pathParameters: [
           new PathParameter('categoryId', 'integer', 'Category ID'),
           new PathParameter('productId', 'string', 'Product UUID'),
       ]
   )
   ```

   **After:**
   ```php
   new Put(
       controller: ProductController::class,
       controllerAction: 'updateProduct',
       pathParameters: [
           new PathParameter('categoryId', 'integer', 'Category ID', '\d+'),
           new PathParameter('productId', 'string', 'Product UUID', '[a-f0-9-]{36}'),
       ]
   )
   ```

4. **Handle Missing PathParameters**

   If an operation had `urlRequirements` but no `pathParameters`, create them:

   **Before:**
   ```php
   new Delete(
       controller: ItemController::class,
       controllerAction: 'deleteItem',
       urlRequirements: ['id' => '\d+']
   )
   ```

   **After:**
   ```php
   new Delete(
       controller: ItemController::class,
       controllerAction: 'deleteItem',
       pathParameters: [
           new PathParameter('id', 'integer', 'Item ID', '\d+'),
       ]
   )
   ```

### Benefits of the New Approach

1. **Better Organization**: URL constraints are now co-located with their parameter definitions
2. **Improved Consistency**: Single source of truth for path parameter information
3. **Enhanced Readability**: Parameter name, type, description, and constraint are all in one place
4. **Reduced Duplication**: No need to define parameter names in multiple places

### Common Constraint Patterns

Here are some common regex patterns you might be migrating:

- **Integer IDs**: `\d+`
- **UUIDs**: `[a-f0-9-]{36}` or `[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}`
- **Slugs**: `[a-z0-9-]+`
- **Alphanumeric codes**: `[A-Z0-9]+`
- **Date formats**: `\d{4}-\d{2}-\d{2}`

### Validation

After migration, ensure your changes work correctly:

1. **Run tests** to verify functionality
2. **Check route generation** to ensure constraints are applied
3. **Test API endpoints** to confirm URL validation works as expected

### Need Help?

If you encounter issues during migration:

1. Check that parameter names in `PathParameter` objects match the keys from your old `urlRequirements`
2. Verify that constraint patterns are valid regex expressions
3. Ensure all operations that had `urlRequirements` have been updated
4. Run your test suite to catch any missed migrations
