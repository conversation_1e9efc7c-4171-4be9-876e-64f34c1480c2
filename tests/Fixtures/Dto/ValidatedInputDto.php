<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Tests\Fixtures\Dto;

use Symfony\Component\Validator\Constraints as Assert;

class ValidatedInputDto
{
    #[Assert\NotBlank]
    #[Assert\Length(min: 3, max: 50)]
    public string $name;

    #[Assert\Range(min: 1, max: 100)]
    public ?int $value = null;

    public function __construct(string $name = '', ?int $value = null)
    {
        $this->name = $name;
        $this->value = $value;
    }
}
