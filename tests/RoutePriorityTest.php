<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Tests;

use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use PreZero\ApiBundle\ApiResources;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Operation\Put;
use PreZero\ApiBundle\Attribute\Operation\Delete;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\GetArray;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Configuration;
use PreZero\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Enum\Pagination;
use PreZero\ApiBundle\Metadata\OperationMetadata;
use PreZero\ApiBundle\Metadata\PathParameterMetadata;
use PreZero\ApiBundle\Metadata\ResourceMetadata;
use PreZero\ApiBundle\RouteLoader;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Routing\Route;
use Symfony\Component\Routing\RouteCollection;

class RoutePriorityTest extends TestCase
{
    private MockObject&Configuration $configuration;
    private MockObject&ApiResources $apiResources;
    private MockObject&Filesystem $filesystem;
    private RouteLoader $routeLoader;
    private string $projectDir;

    protected function setUp(): void
    {
        $this->configuration = $this->createMock(Configuration::class);
        $this->apiResources = $this->createMock(ApiResources::class);
        $this->filesystem = $this->createMock(Filesystem::class);
        $this->projectDir = PROJECT_DIR;

        $this->routeLoader = new RouteLoader(
            $this->configuration,
            $this->apiResources,
            $this->filesystem,
            $this->projectDir
        );
    }

    public function testOperationAttributesAcceptPriorityParameter(): void
    {
        // Test that all operation attributes accept priority parameter
        $operations = [
            new Get(
                controller: 'App\\Controller\\TestController',
                controllerAction: 'get',
                routePriority: 10
            ),
            new Post(
                controller: 'App\\Controller\\TestController',
                controllerAction: 'post',
                routePriority: 20
            ),
            new Put(
                controller: 'App\\Controller\\TestController',
                controllerAction: 'put',
                routePriority: 30
            ),
            new Delete(
                controller: 'App\\Controller\\TestController',
                controllerAction: 'delete',
                routePriority: 40
            ),
            new Patch(
                controller: 'App\\Controller\\TestController',
                controllerAction: 'patch',
                routePriority: 50
            ),
            new GetArray(
                controller: 'App\\Controller\\TestController',
                controllerAction: 'getArray',
                routePriority: 60
            ),
            new GetCollection(
                controller: 'App\\Controller\\TestController',
                controllerAction: 'getCollection',
                routePriority: 70
            ),
        ];

        $expectedPriorities = [10, 20, 30, 40, 50, 60, 70];

        foreach ($operations as $index => $operation) {
            $this->assertEquals($expectedPriorities[$index], $operation->routePriority);
        }
    }

    public function testOperationAttributesDefaultPriorityIsZero(): void
    {
        // Test that default priority is 0 when not specified
        $operations = [
            new Get(
                controller: 'App\\Controller\\TestController',
                controllerAction: 'get'
            ),
            new Post(
                controller: 'App\\Controller\\TestController',
                controllerAction: 'post'
            ),
            new Put(
                controller: 'App\\Controller\\TestController',
                controllerAction: 'put'
            ),
            new Delete(
                controller: 'App\\Controller\\TestController',
                controllerAction: 'delete'
            ),
            new Patch(
                controller: 'App\\Controller\\TestController',
                controllerAction: 'patch'
            ),
            new GetArray(
                controller: 'App\\Controller\\TestController',
                controllerAction: 'getArray'
            ),
            new GetCollection(
                controller: 'App\\Controller\\TestController',
                controllerAction: 'getCollection'
            ),
        ];

        foreach ($operations as $operation) {
            $this->assertEquals(0, $operation->routePriority);
        }
    }

    public function testOperationMetadataStoresPriority(): void
    {
        $operationMetadata = new OperationMetadata(
            name: 'test_operation',
            controller: 'App\\Controller\\TestController',
            controllerAction: 'test',
            security: null,
            summary: 'Test operation',
            description: 'Test description',
            type: Get::class,
            method: 'GET',
            uriTemplate: '/test',
            fullUrlTemplate: '/api/test',
            urlRequirements: [],
            routePriority: 42,
            pathParameters: [],
            queryParameters: [],
            denormalizationContext: [],
            filters: [],
            input: null,
            requestType: ContentType::DTO,
            requestDescription: '',
            headers: [],
            requestOpenApiSchemaName: null,
            pagination: Pagination::NONE,
            perPageOptions: [],
            defaultPerPage: 10,
            responses: [],
            responseDescription: '',
            normalizationContext: [],
            successHttpCode: 200,
            output: null,
            responseType: ContentType::DTO,
            responseOpenApiSchemaName: null,
            responseCodesLogLevel: [],
        );

        $this->assertEquals(42, $operationMetadata->routePriority);
    }

    public function testRouteLoaderSetsPriorityInSymfonyRoute(): void
    {
        $areaName = 'testArea';
        $pathPrefix = '/api/test';
        $areaConfig = [
            'resource_path' => 'src',
            'path_prefix' => $pathPrefix,
        ];
        $bundleConfig = ['areas' => [$areaName => $areaConfig]];

        $this->configuration->method('getBundleConfiguration')->willReturn($bundleConfig);

        $expectedPath = $this->projectDir.'/'.$areaConfig['resource_path'];
        $this->filesystem->method('readlink')
            ->with($expectedPath, true)
            ->willReturn($expectedPath);

        $operationMetadata = new OperationMetadata(
            name: 'high_priority_operation',
            controller: 'App\\Controller\\TestController',
            controllerAction: 'test',
            security: null,
            summary: 'High priority operation',
            description: 'Test operation with high priority',
            type: Get::class,
            method: 'GET',
            uriTemplate: '/test',
            fullUrlTemplate: $pathPrefix.'/test',
            urlRequirements: [],
            routePriority: 100,
            pathParameters: [],
            queryParameters: [],
            denormalizationContext: [],
            filters: [],
            input: null,
            requestType: ContentType::DTO,
            requestDescription: '',
            headers: [],
            requestOpenApiSchemaName: null,
            pagination: Pagination::NONE,
            perPageOptions: [],
            defaultPerPage: 10,
            responses: [],
            responseDescription: '',
            normalizationContext: [],
            successHttpCode: 200,
            output: null,
            responseType: ContentType::DTO,
            responseOpenApiSchemaName: null,
            responseCodesLogLevel: [],
        );

        $resourceMetadata = new ResourceMetadata(
            resourceClassNamespace: 'PreZero\\ApiBundle\\Tests',
            resourceClass: DummyResource::class,
            resourceClassShortName: 'DummyResource',
            name: 'DummyResource',
            area: $areaName,
            identifier: 'id',
            pathPrefix: $pathPrefix,
            tag: 'DummyResource',
            security: null,
            operations: [$operationMetadata],
            perPageOptions: [],
            defaultPerPage: 10
        );

        $this->apiResources->method('getResourcesMetadataForApiArea')
            ->with($areaName)
            ->willReturn([$resourceMetadata]);

        $routeCollection = ($this->routeLoader)();

        $this->assertInstanceOf(RouteCollection::class, $routeCollection);

        $route = $routeCollection->get('high_priority_operation');
        $this->assertInstanceOf(Route::class, $route);

        $this->assertEquals(100, $routeCollection->getPriority('high_priority_operation'));
    }

    public function testNegativePriorityIsSupported(): void
    {
        $operation = new Get(
            controller: 'App\\Controller\\TestController',
            controllerAction: 'get',
            routePriority: -10
        );

        $this->assertEquals(-10, $operation->routePriority);
    }

    public function testMultipleRoutesWithDifferentPriorities(): void
    {
        $areaName = 'testArea';
        $pathPrefix = '/api/test';
        $areaConfig = [
            'resource_path' => 'src',
            'path_prefix' => $pathPrefix,
        ];
        $bundleConfig = ['areas' => [$areaName => $areaConfig]];

        $this->configuration->method('getBundleConfiguration')->willReturn($bundleConfig);

        $expectedPath = $this->projectDir.'/'.$areaConfig['resource_path'];
        $this->filesystem->method('readlink')
            ->with($expectedPath, true)
            ->willReturn($expectedPath);

        // Create operations with different priorities
        $highPriorityOperation = new OperationMetadata(
            name: 'high_priority_operation',
            controller: 'App\\Controller\\TestController',
            controllerAction: 'highPriority',
            security: null,
            summary: 'High priority operation',
            description: 'Test operation with high priority',
            type: Get::class,
            method: 'GET',
            uriTemplate: '/test/special',
            fullUrlTemplate: $pathPrefix.'/test/special',
            urlRequirements: [],
            routePriority: 100,
            pathParameters: [],
            queryParameters: [],
            denormalizationContext: [],
            filters: [],
            input: null,
            requestType: ContentType::DTO,
            requestDescription: '',
            headers: [],
            requestOpenApiSchemaName: null,
            pagination: Pagination::NONE,
            perPageOptions: [],
            defaultPerPage: 10,
            responses: [],
            responseDescription: '',
            normalizationContext: [],
            successHttpCode: 200,
            output: null,
            responseType: ContentType::DTO,
            responseOpenApiSchemaName: null,
            responseCodesLogLevel: [],
        );

        $defaultPriorityOperation = new OperationMetadata(
            name: 'default_priority_operation',
            controller: 'App\\Controller\\TestController',
            controllerAction: 'defaultPriority',
            security: null,
            summary: 'Default priority operation',
            description: 'Test operation with default priority',
            type: Get::class,
            method: 'GET',
            uriTemplate: '/test/{id}',
            fullUrlTemplate: $pathPrefix.'/test/{id}',
            urlRequirements: ['id' => '\d+'],
            routePriority: 0,
            pathParameters: [
                new PathParameterMetadata('id', 'integer', 'The test ID', '\d+')
            ],
            queryParameters: [],
            denormalizationContext: [],
            filters: [],
            input: null,
            requestType: ContentType::DTO,
            requestDescription: '',
            headers: [],
            requestOpenApiSchemaName: null,
            pagination: Pagination::NONE,
            perPageOptions: [],
            defaultPerPage: 10,
            responses: [],
            responseDescription: '',
            normalizationContext: [],
            successHttpCode: 200,
            output: null,
            responseType: ContentType::DTO,
            responseOpenApiSchemaName: null,
            responseCodesLogLevel: [],
        );

        $resourceMetadata = new ResourceMetadata(
            resourceClassNamespace: 'PreZero\\ApiBundle\\Tests',
            resourceClass: DummyResource::class,
            resourceClassShortName: 'DummyResource',
            name: 'DummyResource',
            area: $areaName,
            identifier: 'id',
            pathPrefix: $pathPrefix,
            tag: 'DummyResource',
            security: null,
            operations: [$highPriorityOperation, $defaultPriorityOperation],
            perPageOptions: [],
            defaultPerPage: 10
        );

        $this->apiResources->method('getResourcesMetadataForApiArea')
            ->with($areaName)
            ->willReturn([$resourceMetadata]);

        $routeCollection = ($this->routeLoader)();

        $this->assertInstanceOf(RouteCollection::class, $routeCollection);

        $this->assertEquals(100, $routeCollection->getPriority('high_priority_operation'));
        $this->assertEquals(0, $routeCollection->getPriority('default_priority_operation'));
    }
}
