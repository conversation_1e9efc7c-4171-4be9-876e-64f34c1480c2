<?php

namespace Pre<PERSON>ero\ApiBundle\Tests\Metadata;

use PreZero\ApiBundle\Attribute\Operation\Operation;
use PreZero\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Enum\Pagination;

class DummyOperation extends Operation
{
    public const string HTTP_METHOD = 'GET';

    public function __construct(
        string $controller = 'PreZero\\ApiBundle\\Tests\\Controller\\DummyController',
        string $controllerAction = 'getItem',
        ?string $name = null,
        string $summary = '',
        string $description = '',
        ?string $security = null,
        int $routePriority = 0,
        ?string $uriTemplate = null,
        array $urlRequirements = [],
        ?array $pathParameters = null,
        array $queryParameters = [],
        bool $omitDefaultQueryParameters = false,
        ContentType $requestType = ContentType::EMPTY,
        string $requestDescription = '',
        array $denormalizationContext = [],
        array $filters = [],
        ?string $input = null,
        array $additionalRequestHeaders = [],
        Pagination $pagination = Pagination::NONE,
        string $responseDescription = '',
        array $responses = [],
        ContentType $responseType = ContentType::DTO,
        array $normalizationContext = [],
        ?string $output = null,
        int $successHttpCode = 200,
        ?string $requestOpenApiSchemaName = null,
        ?string $responseOpenApiSchemaName = null,
        array $responseCodesLogLevel = [],
    ) {
        parent::__construct(
            controller: $controller,
            controllerAction: $controllerAction,
            name: $name,
            summary: $summary,
            description: $description,
            security: $security,
            uriTemplate: $uriTemplate,
            urlRequirements: $urlRequirements,
            routePriority: $routePriority,
            pathParameters: $pathParameters,
            queryParameters: $queryParameters,
            omitDefaultQueryParameters: $omitDefaultQueryParameters,
            requestType: $requestType,
            requestDescription: $requestDescription,
            denormalizationContext: $denormalizationContext,
            filters: $filters,
            input: $input,
            additionalRequestHeaders: $additionalRequestHeaders,
            requestOpenApiSchemaName: $requestOpenApiSchemaName,
            pagination: $pagination,
            perPageOptions: null,
            defaultPerPage: null,
            responseDescription: $responseDescription,
            responses: $responses,
            responseType: $responseType,
            normalizationContext: $normalizationContext,
            output: $output,
            successHttpCode: $successHttpCode,
            responseOpenApiSchemaName: $responseOpenApiSchemaName,
            responseCodesLogLevel: $responseCodesLogLevel,
        );
    }
}
