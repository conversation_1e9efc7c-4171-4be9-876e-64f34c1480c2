<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Tests;

use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use PreZero\ApiBundle\ApiResources;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Configuration;
use PreZero\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Enum\Pagination;
use PreZero\ApiBundle\Metadata\OperationMetadata;
use PreZero\ApiBundle\Metadata\PathParameterMetadata;
use PreZero\ApiBundle\Metadata\ResourceMetadata;
use PreZero\ApiBundle\RouteLoader;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Routing\Route;
use Symfony\Component\Routing\RouteCollection;

class RouteLoaderTest extends TestCase
{
    private MockObject&Configuration $configuration;
    private MockObject&ApiResources $apiResources;
    private MockObject&Filesystem $filesystem;
    private RouteLoader $routeLoader;
    private string $projectDir;

    protected function setUp(): void
    {
        $this->configuration = $this->createMock(Configuration::class);
        $this->apiResources = $this->createMock(ApiResources::class);
        $this->filesystem = $this->createMock(Filesystem::class);
        $this->projectDir = PROJECT_DIR;

        $this->routeLoader = new RouteLoader(
            $this->configuration,
            $this->apiResources,
            $this->filesystem,
            $this->projectDir
        );
    }

    public function testInvokeGeneratesRoutes(): void
    {
        $areaName = 'testArea';
        $pathPrefix = '/api/test';
        $areaConfig = [
            'resource_path' => 'src',
            'path_prefix' => $pathPrefix,
        ];
        $bundleConfig = ['areas' => [$areaName => $areaConfig]];

        $this->configuration->method('getBundleConfiguration')->willReturn($bundleConfig);

        $expectedPath = $this->projectDir.'/'.$areaConfig['resource_path'];
        $this->filesystem->method('readlink')
            ->with($expectedPath, true)
            ->willReturn($expectedPath);

        $operationMetadata = new OperationMetadata(
            name: 'get_dummy_item',
            controller: 'App\\Controller\\DummyController',
            controllerAction: 'getItem',
            security: null,
            summary: 'Get a dummy item',
            description: 'Retrieves a single dummy item by its ID.',
            type: Get::class,
            method: 'GET',
            uriTemplate: '/items/{id}',
            fullUrlTemplate: $pathPrefix.'/items/{id}',
            urlRequirements: ['id' => '\d+'],
            routePriority: 0,
            pathParameters: [
                new PathParameterMetadata('id', 'integer', 'The item ID', '\d+')
            ],
            queryParameters: [],
            denormalizationContext: [],
            filters: [],
            input: null,
            requestType: ContentType::DTO,
            requestDescription: '',
            headers: [],
            requestOpenApiSchemaName: null,

            // Response
            pagination: Pagination::NONE,
            perPageOptions: [],
            defaultPerPage: 10,
            responses: [],
            responseDescription: '',
            normalizationContext: [],
            successHttpCode: 200,
            output: null,
            responseType: ContentType::DTO,
            responseOpenApiSchemaName: null,
            responseCodesLogLevel: [],
        );

        $resourceMetadata = new ResourceMetadata(
            resourceClassNamespace: 'PreZero\\ApiBundle\\Tests',
            resourceClass: DummyResource::class,
            resourceClassShortName: 'DummyResource',
            name: 'DummyResource',
            area: $areaName,
            identifier: 'id',
            pathPrefix: $pathPrefix,
            tag: 'DummyResource',
            security: null,
            operations: [$operationMetadata],
            perPageOptions: [],
            defaultPerPage: 10
        );

        $this->apiResources->method('getResourcesMetadataForApiArea')
            ->with($areaName)
            ->willReturn([$resourceMetadata]);

        $routeCollection = ($this->routeLoader)();

        self::assertInstanceOf(RouteCollection::class, $routeCollection);
        self::assertCount(1, $routeCollection);

        $routeKey = 'get_dummy_item';
        $route = $routeCollection->get($routeKey);

        self::assertInstanceOf(Route::class, $route, "Route with key '{$routeKey}' not found in collection.");
        self::assertSame($pathPrefix.'/items/{id}', $route->getPath());
        self::assertSame(['GET'], $route->getMethods());
        self::assertEquals(['id' => '\d+'], $route->getRequirements());
        self::assertEquals([
            '_controller' => 'App\\Controller\\DummyController::getItem',
            '_format' => 'json',
            '_stateless' => true,
            '_api_area' => $areaName,
            '_api_resource_class' => DummyResource::class,
            '_api_operation_name' => 'get_dummy_item',
        ], $route->getDefaults());
    }

    public function testInvokeThrowsExceptionForInvalidResourcePath(): void
    {
        $areaName = 'invalidArea';
        $invalidPath = 'non/existent/path';
        $areaConfig = [
            'resource_path' => $invalidPath,
            'path_prefix' => '/api/invalid',
        ];
        $bundleConfig = ['areas' => [$areaName => $areaConfig]];

        $this->configuration->method('getBundleConfiguration')->willReturn($bundleConfig);

        $expectedPath = $this->projectDir.'/'.$invalidPath;
        $this->filesystem->method('readlink')
            ->with($expectedPath, true)
            ->willReturn(null);

        $this->expectException(\LogicException::class);
        $this->expectExceptionMessage(sprintf(
            'Api area "%s" resource path "%s" does not exist. Check configuration under "api.areas"',
            $areaName,
            $invalidPath
        ));

        ($this->routeLoader)();
    }
}

// Dummy class used for testing metadata association
#[ApiResource(area: 'testArea', operations: [])]
class DummyResource
{
}
