<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Tests\Documentation;

use Dok<PERSON>\OpenApi\In;
use Dokky\OpenApi\Operation;
use Dokky\OpenApi\Parameter;
use Dok<PERSON>\OpenApi\Schema;
use PHPUnit\Framework\TestCase;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Documentation\Dokky;
use PreZero\ApiBundle\Documentation\RequestDescriber;
use PreZero\ApiBundle\Enum\FilterType;
use PreZero\ApiBundle\Metadata\FilterMetadata;
use PreZero\ApiBundle\Metadata\OperationMetadata;

class RequestDescriberTest extends TestCase
{
    private RequestDescriber $requestDescriber;
    private \PHPUnit\Framework\MockObject\MockObject|Dokky $dokkyMock;

    protected function setUp(): void
    {
        $this->dokkyMock = $this->createMock(Dokky::class);
        $this->requestDescriber = new RequestDescriber($this->dokkyMock);
    }

    public function testDescribeQueryParametersHandlesMultipleValueFilter(): void
    {
        // Arrange
        $filterMetadata = new FilterMetadata(
            parameterName: 'status',
            filterType: FilterType::STRING_EXACT,
            fieldName: 'statusField',
            required: false,
            parameterType: 'string',
            parameterDescription: 'Filter by status',
            parameterFormat: null,
            parameterEnumValues: ['active', 'inactive'],
            validators: [],
            multiple: true // Key part: filter allows multiple values
        );

        $operationMetadata = new OperationMetadata(
            name: 'test_dummy_get_collection_0',
            controller: 'App\Controller\DummyController',
            controllerAction: 'getCollection',
            security: null,
            summary: 'Get collection of dummies',
            description: '', // Provide an empty string instead of null
            type: GetCollection::class,
            method: 'GET',
            uriTemplate: '/dummies',
            fullUrlTemplate: '/test-api/dummies',
            urlRequirements: [],
            routePriority: 0,
            pathParameters: [],
            queryParameters: [], // No standard query params for simplicity
            denormalizationContext: [],
            filters: [$filterMetadata], // Add our multi-value filter
            input: null,
            requestType: \PreZero\ApiBundle\Enum\ContentType::EMPTY,
            requestDescription: '', // Provide an empty string instead of null
            headers: [],
            requestOpenApiSchemaName: null,
            pagination: \PreZero\ApiBundle\Enum\Pagination::NONE,
            perPageOptions: [],
            defaultPerPage: 10,
            responses: [],
            responseDescription: '', // Provide an empty string instead of null
            normalizationContext: [],
            successHttpCode: 200,
            output: null,
            responseType: \PreZero\ApiBundle\Enum\ContentType::DTO,
            responseOpenApiSchemaName: null,
            responseCodesLogLevel: [],
        );

        $openApiOperation = new Operation();

        // Act
        $this->requestDescriber->describeQueryParameters($operationMetadata, $openApiOperation);

        // Assert
        $this->assertCount(1, $openApiOperation->parameters);
        $this->assertInstanceOf(Parameter::class, $openApiOperation->parameters[0]);

        /** @var Parameter $parameter */
        $parameter = $openApiOperation->parameters[0];

        $this->assertSame('status[]', $parameter->name); // Expect name to end with []
        $this->assertSame(In::QUERY, $parameter->in);
        $this->assertFalse($parameter->required);
        $this->assertSame('Filter by status', $parameter->description);

        $this->assertInstanceOf(Schema::class, $parameter->schema);
        $this->assertSame(Schema\Type::ARRAY, $parameter->schema->type);

        $this->assertInstanceOf(Schema::class, $parameter->schema->items);
        $this->assertSame(Schema\Type::STRING, $parameter->schema->items->type);
        $this->assertSame(['active', 'inactive'], $parameter->schema->items->enum);
    }
}
