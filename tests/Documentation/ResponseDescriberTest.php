<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Tests\Documentation;

use <PERSON>k<PERSON>\ComponentsRegistry;
use Dokky\OpenApi\MediaType;
use Dokky\OpenApi\Operation;
use Dok<PERSON>\OpenApi\Response;
use Dok<PERSON>\OpenApi\Schema;
use Dok<PERSON>\Undefined;
use PHPUnit\Framework\TestCase;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\ErrorResponse\ApiErrorResponse;
use PreZero\ApiBundle\Documentation\Dokky;
use PreZero\ApiBundle\Documentation\ResponseDescriber;
use PreZero\ApiBundle\Metadata\OperationMetadata;
use PreZero\ApiBundle\Metadata\ResourceMetadata;
use PreZero\ApiBundle\Metadata\ResponseMetadata;
use PreZero\ApiBundle\Attribute\Operation\GetArray;
use PreZero\ApiBundle\Tests\Fixtures\Dto\ValidatedInputDto;

class ResponseDescriberTest extends TestCase
{
    private ResponseDescriber $responseDescriber;
    private \PHPUnit\Framework\MockObject\MockObject|Dokky $dokkyMock;

    protected function setUp(): void
    {
        $this->dokkyMock = $this->createMock(Dokky::class);
        $this->responseDescriber = new ResponseDescriber($this->dokkyMock);
    }

    public function testDescribeResponsesIncludesValidationErrorResponse(): void
    {
        // Arrange
        $operationMetadata = new OperationMetadata(
            name: 'test_dummy_post_0',
            controller: 'App\Controller\DummyController',
            controllerAction: 'postAction',
            security: null,
            summary: 'Create a dummy',
            description: '',
            type: Post::class,
            method: 'POST',
            uriTemplate: '/dummies',
            fullUrlTemplate: '/test-api/dummies',
            urlRequirements: [],
            routePriority: 0,
            pathParameters: [],
            queryParameters: [],
            denormalizationContext: [],
            filters: [],
            input: ValidatedInputDto::class,
            requestType: \PreZero\ApiBundle\Enum\ContentType::DTO,
            requestDescription: '',
            headers: [],
            requestOpenApiSchemaName: null,
            pagination: \PreZero\ApiBundle\Enum\Pagination::NONE,
            perPageOptions: [],
            defaultPerPage: 10,
            responses: [],
            responseDescription: '',
            normalizationContext: [],
            successHttpCode: 201,
            output: null,
            responseType: \PreZero\ApiBundle\Enum\ContentType::EMPTY,
            responseOpenApiSchemaName: null,
            responseCodesLogLevel: [],
        );

        $openApiOperation = new Operation();

        $componentsRegistryMock = $this->createMock(ComponentsRegistry::class);
        $componentsRegistryMock->method('getSchemaReference')
            ->with(ApiErrorResponse::class, [ApiErrorResponse::GROUP_DEFAULT])
            ->willReturn('#/components/schemas/ApiErrorResponse_Default');

        $componentsRegistryMock->method('getNamedSchemaReference')
            ->with(ApiErrorResponse::class, 'ApiValidationError', [ApiErrorResponse::GROUP_VALIDATION])
            ->willReturn('#/components/schemas/ApiValidationError');

        // This test method re-initializes dokkyMock and responseDescriber locally.
        $dokkyMockForThisTest = $this->createMock(Dokky::class);
        $dokkyMockForThisTest->method('componentsRegistry')->willReturn($componentsRegistryMock);
        $this->responseDescriber = new ResponseDescriber($dokkyMockForThisTest);

        // Act
        $apiResource = new \PreZero\ApiBundle\Attribute\ApiResource('test-area', []);
        $resourceMetadata = new ResourceMetadata(
            resourceClassNamespace: 'PreZero\\ApiBundle\\Tests\\Fixtures\\Dto',
            resourceClass: ValidatedInputDto::class,
            resourceClassShortName: 'ValidatedInputDto',
            name: 'ValidatedInputDto',
            area: $apiResource->area,
            identifier: $apiResource->identifier,
            pathPrefix: '/test-area-api',
            tag: $apiResource->tag,
            security: $apiResource->security,
            operations: [],
            perPageOptions: [],
            defaultPerPage: 10,
        );
        $this->responseDescriber->describeResponse($resourceMetadata, $operationMetadata, $openApiOperation);

        // Assert
        $this->assertArrayHasKey('422', $openApiOperation->responses);
        $response422 = $openApiOperation->responses['422'];
        $this->assertInstanceOf(Response::class, $response422);
        $this->assertSame('Request validation failed', $response422->description);

        $this->assertArrayHasKey('application/json', $response422->content);
        $mediaType = $response422->content['application/json'];
        $this->assertInstanceOf(MediaType::class, $mediaType);
        $schema = $mediaType->schema;
        $this->assertInstanceOf(Schema::class, $schema);
        // Assert that the schema is now a named reference
        $this->assertSame('#/components/schemas/ApiValidationError', $schema->ref);
        // Assert that inline properties are not set for a reference schema
        $this->assertEquals(Undefined::VALUE, $schema->type);
        $this->assertEquals(Undefined::VALUE, $schema->properties);
    }

    public function testDescribeResponsePrioritizesCustomResponseContext(): void
    {
        // Arrange
        $operationContext = ['groups' => ['operation_group']];
        $responseContext = ['groups' => ['response_group']];

        $customResponse = new \PreZero\ApiBundle\Attribute\Value\Response(
            httpCode: 202,
            description: 'Custom Accepted Response',
            output: \stdClass::class,
            normalizationContext: $responseContext,
        );

        $operationMetadata = new OperationMetadata(
            name: 'test_dummy_post_1',
            controller: 'App\Controller\DummyController',
            controllerAction: 'postAction',
            security: null,
            summary: 'Create a dummy with custom response',
            description: '',
            type: Post::class,
            method: 'POST',
            uriTemplate: '/dummies',
            fullUrlTemplate: '/test-api/dummies',
            urlRequirements: [],
            routePriority: 0,
            pathParameters: [],
            queryParameters: [],
            denormalizationContext: [],
            filters: [],
            input: ValidatedInputDto::class,
            requestType: \PreZero\ApiBundle\Enum\ContentType::DTO,
            requestDescription: '',
            headers: [],
            requestOpenApiSchemaName: null,
            pagination: \PreZero\ApiBundle\Enum\Pagination::NONE,
            perPageOptions: [],
            defaultPerPage: 10,
            responses: [ResponseMetadata::fromAttribute($customResponse)], // Add custom response metadata
            responseDescription: 'Default description', // This should be overridden by custom
            normalizationContext: $operationContext, // Operation level context
            successHttpCode: 201, // Default success code
            output: ValidatedInputDto::class, // Default output
            responseType: \PreZero\ApiBundle\Enum\ContentType::DTO,
            responseOpenApiSchemaName: null,
            responseCodesLogLevel: [],
        );

        $openApiOperation = new Operation();

        $componentsRegistryMock = $this->createMock(ComponentsRegistry::class);
        // Use willReturnCallback for more flexible matching
        $componentsRegistryMock->method('getSchemaReference')
                               ->willReturnCallback(function (string $className, ?array $groups) use ($responseContext) {
                                   // Check if the call matches the expected custom response context
                                   if (\stdClass::class === $className && ($groups === ($responseContext['groups'] ?? null))) {
                                       return '#/components/schemas/StdClassResponseGroup'; // Expected ref for custom response
                                   }

                                   // Return a generic ref for other calls
                                   return '#/components/schemas/GenericSchemaFor_'.str_replace('\\', '_', $className);
                               });

        // Modify Dokky mock setup
        $this->dokkyMock = $this->createMock(Dokky::class);
        $this->dokkyMock->method('componentsRegistry')->willReturn($componentsRegistryMock);
        $this->responseDescriber = new ResponseDescriber($this->dokkyMock);

        $apiResource = new \PreZero\ApiBundle\Attribute\ApiResource('test-area', []);
        $resourceMetadata = new ResourceMetadata(
            resourceClassNamespace: 'PreZero\\ApiBundle\\Tests\\Fixtures\\Dto',
            resourceClass: ValidatedInputDto::class,
            resourceClassShortName: 'ValidatedInputDto',
            name: 'ValidatedInputDto',
            area: $apiResource->area,
            identifier: $apiResource->identifier,
            pathPrefix: '/test-area-api',
            tag: $apiResource->tag,
            security: $apiResource->security,
            operations: [],
            perPageOptions: [],
            defaultPerPage: 10,
        );

        // Act
        $this->responseDescriber->describeResponse($resourceMetadata, $operationMetadata, $openApiOperation);

        // Assert
        $this->assertArrayHasKey('202', $openApiOperation->responses);
        $response202 = $openApiOperation->responses['202'];
        $this->assertInstanceOf(Response::class, $response202);
        $this->assertSame('Custom Accepted Response', $response202->description);

        $this->assertArrayHasKey('application/json', $response202->content);
        $mediaType = $response202->content['application/json'];
        $this->assertInstanceOf(MediaType::class, $mediaType);
        $schema = $mediaType->schema;
        $this->assertInstanceOf(Schema::class, $schema);
        $this->assertSame('#/components/schemas/StdClassResponseGroup', $schema->ref);

        // Also check that default error responses are still present
        $this->assertArrayHasKey('400', $openApiOperation->responses);
        $this->assertArrayHasKey('422', $openApiOperation->responses);
    }

    public function testDescribeResponseInheritsOperationContextWhenNull(): void
    {
        // Arrange
        $operationContext = ['groups' => ['operation_group']];
        $responseContext = null;

        $customResponse = new \PreZero\ApiBundle\Attribute\Value\Response(
            httpCode: 203,
            description: 'Custom Non-Authoritative Response',
            output: \stdClass::class,
            normalizationContext: $responseContext, // Null context
        );

        $operationMetadata = new OperationMetadata(
            name: 'test_dummy_post_2',
            controller: 'App\Controller\DummyController',
            controllerAction: 'postAction',
            security: null,
            summary: 'Create a dummy with inherited response context',
            description: '',
            type: Post::class,
            method: 'POST',
            uriTemplate: '/dummies',
            fullUrlTemplate: '/test-api/dummies',
            urlRequirements: [],
            routePriority: 0,
            pathParameters: [],
            queryParameters: [],
            denormalizationContext: [],
            filters: [],
            input: ValidatedInputDto::class,
            requestType: \PreZero\ApiBundle\Enum\ContentType::DTO,
            requestDescription: '',
            headers: [],
            requestOpenApiSchemaName: null,
            pagination: \PreZero\ApiBundle\Enum\Pagination::NONE,
            perPageOptions: [],
            defaultPerPage: 10,
            responses: [ResponseMetadata::fromAttribute($customResponse)],
            responseDescription: '',
            normalizationContext: $operationContext, // Operation level context to inherit
            successHttpCode: 201,
            output: ValidatedInputDto::class,
            responseType: \PreZero\ApiBundle\Enum\ContentType::DTO,
            responseOpenApiSchemaName: null,
            responseCodesLogLevel: [],
        );

        $openApiOperation = new Operation();

        $componentsRegistryMock = $this->createMock(ComponentsRegistry::class);
        $componentsRegistryMock->method('getSchemaReference')
                               ->willReturnCallback(function (string $className, ?array $groups) use ($operationContext) {
                                   // Check if the call uses the inherited operation context
                                   if (\stdClass::class === $className && ($groups === ($operationContext['groups'] ?? null))) {
                                       return '#/components/schemas/StdClassOperationGroup'; // Expected ref for inherited context
                                   }

                                   return '#/components/schemas/GenericSchemaFor_'.str_replace('\\', '_', $className);
                               });

        $this->dokkyMock = $this->createMock(Dokky::class);
        $this->dokkyMock->method('componentsRegistry')->willReturn($componentsRegistryMock);
        $this->responseDescriber = new ResponseDescriber($this->dokkyMock);

        $apiResource = new \PreZero\ApiBundle\Attribute\ApiResource('test-area', []);
        $resourceMetadata = new ResourceMetadata(
            resourceClassNamespace: 'PreZero\\ApiBundle\\Tests\\Fixtures\\Dto',
            resourceClass: ValidatedInputDto::class,
            resourceClassShortName: 'ValidatedInputDto',
            name: 'ValidatedInputDto',
            area: $apiResource->area,
            identifier: $apiResource->identifier,
            pathPrefix: '/test-area-api',
            tag: $apiResource->tag,
            security: $apiResource->security,
            operations: [],
            perPageOptions: [],
            defaultPerPage: 10,
        );

        // Act
        $this->responseDescriber->describeResponse($resourceMetadata, $operationMetadata, $openApiOperation);

        // Assert
        $this->assertArrayHasKey('203', $openApiOperation->responses);
        $response203 = $openApiOperation->responses['203'];
        $this->assertInstanceOf(Response::class, $response203);
        $this->assertSame('Custom Non-Authoritative Response', $response203->description);

        $this->assertArrayHasKey('application/json', $response203->content);
        $mediaType = $response203->content['application/json'];
        $this->assertInstanceOf(MediaType::class, $mediaType);
        $schema = $mediaType->schema;
        $this->assertInstanceOf(Schema::class, $schema);
        // Check that the schema ref corresponds to the one generated using the operation's context
        $this->assertSame('#/components/schemas/StdClassOperationGroup', $schema->ref);
    }

    public function testDescribeResponseUsesEmptyContextWhenArray(): void
    {
        // Arrange
        $operationContext = ['groups' => ['operation_group']];
        $responseContext = [];

        $customResponse = new \PreZero\ApiBundle\Attribute\Value\Response(
            httpCode: 205,
            description: 'Custom Reset Content Response',
            output: \stdClass::class,
            normalizationContext: $responseContext, // Empty array context
        );

        $operationMetadata = new OperationMetadata(
            name: 'test_dummy_post_3',
            controller: 'App\Controller\DummyController',
            controllerAction: 'postAction',
            security: null,
            summary: 'Create a dummy with empty response context',
            description: '',
            type: Post::class,
            method: 'POST',
            uriTemplate: '/dummies',
            fullUrlTemplate: '/test-api/dummies',
            urlRequirements: [],
            routePriority: 0,
            pathParameters: [],
            queryParameters: [],
            denormalizationContext: [],
            filters: [],
            input: ValidatedInputDto::class,
            requestType: \PreZero\ApiBundle\Enum\ContentType::DTO,
            requestDescription: '',
            headers: [],
            requestOpenApiSchemaName: null,
            pagination: \PreZero\ApiBundle\Enum\Pagination::NONE,
            perPageOptions: [],
            defaultPerPage: 10,
            responses: [ResponseMetadata::fromAttribute($customResponse)],
            responseDescription: '',
            normalizationContext: $operationContext, // Operation level context to be overridden
            successHttpCode: 201,
            output: ValidatedInputDto::class,
            responseType: \PreZero\ApiBundle\Enum\ContentType::DTO,
            responseOpenApiSchemaName: null,
            responseCodesLogLevel: [],
        );

        $openApiOperation = new Operation();

        $componentsRegistryMock = $this->createMock(ComponentsRegistry::class);
        $componentsRegistryMock->method('getSchemaReference')
                               ->willReturnCallback(function (string $className, ?array $groups) {
                                   // Check if the call uses the empty context (groups should be null or empty after extraction)
                                   if (\stdClass::class === $className && empty($groups)) {
                                       return '#/components/schemas/StdClassEmptyGroup'; // Expected ref for empty context
                                   }

                                   return '#/components/schemas/GenericSchemaFor_'.str_replace('\\', '_', $className);
                               });

        $this->dokkyMock = $this->createMock(Dokky::class);
        $this->dokkyMock->method('componentsRegistry')->willReturn($componentsRegistryMock);
        $this->responseDescriber = new ResponseDescriber($this->dokkyMock);

        $apiResource = new \PreZero\ApiBundle\Attribute\ApiResource('test-area', []);
        $resourceMetadata = new ResourceMetadata(
            resourceClassNamespace: 'PreZero\\ApiBundle\\Tests\\Fixtures\\Dto',
            resourceClass: ValidatedInputDto::class,
            resourceClassShortName: 'ValidatedInputDto',
            name: 'ValidatedInputDto',
            area: $apiResource->area,
            identifier: $apiResource->identifier,
            pathPrefix: '/test-area-api',
            tag: $apiResource->tag,
            security: $apiResource->security,
            operations: [],
            perPageOptions: [],
            defaultPerPage: 10,
        );

        // Act
        $this->responseDescriber->describeResponse($resourceMetadata, $operationMetadata, $openApiOperation);

        // Assert
        $this->assertArrayHasKey('205', $openApiOperation->responses);
        $response205 = $openApiOperation->responses['205'];
        $this->assertInstanceOf(Response::class, $response205);
        $this->assertSame('Custom Reset Content Response', $response205->description);

        $this->assertArrayHasKey('application/json', $response205->content);
        $mediaType = $response205->content['application/json'];
        $this->assertInstanceOf(MediaType::class, $mediaType);
        $schema = $mediaType->schema;
        $this->assertInstanceOf(Schema::class, $schema);
        // Check that the schema ref corresponds to the one generated using the empty context
        $this->assertSame('#/components/schemas/StdClassEmptyGroup', $schema->ref);
    }

    public function testDescribeResponseForGetArrayReturnsJsonArray(): void
    {
        // Arrange
        $operationMetadata = new OperationMetadata(
            name: 'test_dummy_get_array_0',
            controller: 'App\Controller\DummyController',
            controllerAction: 'getArrayAction',
            security: null,
            summary: 'Get a dummy array',
            description: '',
            type: GetArray::class,
            method: 'GET',
            uriTemplate: '/dummies-array',
            fullUrlTemplate: '/test-api/dummies-array',
            urlRequirements: [],
            routePriority: 0,
            pathParameters: [],
            queryParameters: [],
            denormalizationContext: [],
            filters: [],
            input: null,
            requestType: \PreZero\ApiBundle\Enum\ContentType::EMPTY,
            requestDescription: '',
            headers: [],
            requestOpenApiSchemaName: null,
            pagination: \PreZero\ApiBundle\Enum\Pagination::NONE,
            perPageOptions: [],
            defaultPerPage: 10,
            responses: [],
            responseDescription: 'A list of dummies',
            normalizationContext: [],
            successHttpCode: 200,
            output: ValidatedInputDto::class,
            responseType: \PreZero\ApiBundle\Enum\ContentType::DTO,
            responseOpenApiSchemaName: null,
            responseCodesLogLevel: [],
        );

        $openApiOperation = new Operation();

        $componentsRegistryMock = $this->createMock(ComponentsRegistry::class);
        $componentsRegistryMock->method('getSchemaReference')
            ->willReturnMap([
                [ValidatedInputDto::class, null, '#/components/schemas/ValidatedInputDto'],
                [ApiErrorResponse::class, [ApiErrorResponse::GROUP_DEFAULT], '#/components/schemas/ApiErrorResponse_Default'],
                [ApiErrorResponse::class, [ApiErrorResponse::GROUP_VALIDATION], '#/components/schemas/ApiValidationError'],
            ]);

        $this->dokkyMock->method('componentsRegistry')->willReturn($componentsRegistryMock);

        $apiResource = new \PreZero\ApiBundle\Attribute\ApiResource('test-area', []);
        $resourceMetadata = new ResourceMetadata(
            resourceClassNamespace: 'PreZero\\ApiBundle\\Tests\\Fixtures\\Dto',
            resourceClass: ValidatedInputDto::class,
            resourceClassShortName: 'ValidatedInputDto',
            name: 'ValidatedInputDto',
            area: $apiResource->area,
            identifier: $apiResource->identifier,
            pathPrefix: '/test-area-api',
            tag: $apiResource->tag,
            security: $apiResource->security,
            operations: [],
            perPageOptions: [],
            defaultPerPage: 10,
        );

        // Act
        $this->responseDescriber->describeResponse($resourceMetadata, $operationMetadata, $openApiOperation);

        // Assert
        $this->assertArrayHasKey('200', $openApiOperation->responses);
        $response200 = $openApiOperation->responses['200'];
        $this->assertInstanceOf(Response::class, $response200);
        $this->assertSame('A list of dummies', $response200->description);

        $this->assertArrayHasKey('application/json', $response200->content);
        $mediaType = $response200->content['application/json'];
        $this->assertInstanceOf(MediaType::class, $mediaType);
        $schema = $mediaType->schema;
        $this->assertInstanceOf(Schema::class, $schema);
        $this->assertEquals('array', $schema->type->value);
        $this->assertInstanceOf(Schema::class, $schema->items);
        $this->assertSame('#/components/schemas/ValidatedInputDto', $schema->items->ref);
    }
}
