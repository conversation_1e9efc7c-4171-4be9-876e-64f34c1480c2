<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Tests\Controller;

class DummyController
{
    public function getItem(): void
    {
    }

    public function getItems(): void
    {
    }

    public function createItem(): void
    {
    }

    public function updateItem(): void
    {
    }

    public function patchItem(): void
    {
    }

    public function deleteItem(): void
    {
    }

    public function createSubItem(): void
    {
    }
}
