name: Tests

on:
    push:
        branches: [ master ]
    pull_request:

jobs:
    test:
        runs-on: prezero-github-runner

        steps:
            -   name: Checkout code
                uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

            -   name: Install package dependencies
                run: sudo apt-get update && sudo apt-get upgrade -y && sudo apt-get install -y zstd

            -   name: Set up php 8.4
                uses: shivammathur/setup-php@9e72090525849c5e82e596468b86eb55e9cc5401 # 2.32.0
                with:
                    php-version: '8.4'
                    coverage: xdebug
                    tools: composer:v2

            -   name: Validate composer.json and composer.lock
                run: composer validate --strict

            -   name: Get Composer Cache Directory
                id: composer-cache
                run: |
                    echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

            -   uses: actions/cache@v4
                with:
                    path: ${{ steps.composer-cache.outputs.dir }}
                    key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
                    restore-keys: |
                        ${{ runner.os }}-composer-

            -   name: Install dependencies
                run: composer install --prefer-dist --no-progress --no-suggest

            -   name: Run tests
                run: vendor/bin/phpunit
