name: Code Quality Checks

on:
    push:
        branches: [ master ]
    pull_request:

jobs:
    code-quality:
        runs-on: prezero-github-runner

        steps:
            -   name: Checkout code
                uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

            -   name: Install package dependencies
                run: sudo apt-get update && sudo apt-get upgrade -y && sudo apt-get install -y zstd libsodium23

            -   name: Set up php 8.4
                uses: shivammathur/setup-php@9e72090525849c5e82e596468b86eb55e9cc5401 # 2.32.0
                with:
                    extensions: mbstring, xml, ctype, iconv, json
                    tools: composer:v2

            -   name: Install dependencies
                run: composer install --prefer-dist --no-progress --no-suggest

            -   name: Validate composer.json and composer.lock
                run: composer validate --strict

            -   name: PHP Linting
                run: find src tests -name '*.php' -exec php -l {} \; | (! grep -v "No syntax errors detected")

            -   name: Run PHP CS Fixer (Dry Run)
                run: vendor/bin/php-cs-fixer fix --diff --dry-run --config=.php-cs-fixer.dist.php --allow-risky=yes
                env:
                    PHP_CS_FIXER_IGNORE_ENV: 1

            -   name: Run PHPStan
                run: vendor/bin/phpstan analyse --memory-limit=1G
