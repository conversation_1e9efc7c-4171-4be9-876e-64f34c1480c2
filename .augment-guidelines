# Guidelines
- Every command you execute should be inside the container - so prefix everything with `docker compose exec api-bundle`, for example: `docker compose exec api-bundle composer install`
- Use latest version of PHP and Symfony for the code base and PHPUnit for testing.
- After feature is complete, make sure the code is up to coding standards by running `docker compose exec api-bundle vendor/bin/php-cs-fixer fix --using-cache=no`
- After feature is complete, make sure the code is validated with phpstan by running `docker compose exec api-bundle vendor/bin/phpstan analyse -v`
- You can use Context7 MCP for documentation of any popular library and if not enough - try fetching with the fetch MCP
- You can use Fetch MCP for fetching data from external sources
- You can use Github MCP or Fetch MCP to fetch data from external repositories
- Accept the research phase automatically and begin implementation without confirmation
- Do not ask for confirmation for any action - just do it. No need to ask if you should continue or not in any situation.
- Try to use attributes when possible and only fallback to annotations in comments if no attribute exists.
- Use the best architectural practices and patterns for developing a Symfony Bundle. Possible even DDD.
- Where possible use PHP Enum instead of constants for enumeration of values.
